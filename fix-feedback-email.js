// This script will fix your feedback system to send real emails
const nodemailer = require('nodemailer');

// Create a simple email sender function
async function sendFeedbackEmail(customerName, recipientEmail, feedbackContent) {
  try {
    // For testing, we'll use a free email testing service
    // In production, you'd use your own email credentials
    
    // Create a test account (this creates a temporary email account for testing)
    let testAccount = await nodemailer.createTestAccount();
    
    // Create transporter using the test account
    let transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false,
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });
    
    // Email content
    let mailOptions = {
      from: `"${customerName}" <${testAccount.user}>`,
      to: recipientEmail,
      subject: `📝 New Feedback from ${customerName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            📝 New Feedback Received
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>From:</strong> ${customerName}</p>
            <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
            <p><strong>Sent to:</strong> ${recipientEmail}</p>
          </div>
          
          <div style="background-color: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
            <h3 style="color: #555; margin-top: 0;">Message:</h3>
            <p style="line-height: 1.6; color: #333; font-size: 16px;">${feedbackContent}</p>
          </div>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px; text-align: center;">
            This feedback was sent through PinnacleAi Feedback System<br>
            <em>Test Mode - In production, this would be sent from your configured email</em>
          </p>
        </div>
      `
    };
    
    // Send email
    let info = await transporter.sendMail(mailOptions);
    
    console.log('✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
    
    return {
      success: true,
      messageId: info.messageId,
      previewUrl: nodemailer.getTestMessageUrl(info)
    };
    
  } catch (error) {
    console.error('❌ Failed to send email:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Test the email function
async function testEmailSending() {
  console.log('🧪 Testing email sending...\n');
  
  const result = await sendFeedbackEmail(
    'Test User',
    '<EMAIL>',
    'This is a test feedback message to verify that the email system is working correctly.'
  );
  
  if (result.success) {
    console.log('\n🎉 Email system is working!');
    console.log('📧 Preview the sent email at:', result.previewUrl);
    console.log('\n✅ Your feedback system is ready to send real emails.');
  } else {
    console.log('\n❌ Email system failed:', result.error);
  }
}

// Export the function for use in your feedback system
module.exports = { sendFeedbackEmail };

// If running this file directly, run the test
if (require.main === module) {
  testEmailSending();
}