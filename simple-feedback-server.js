const express = require('express');
const nodemailer = require('nodemailer');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Email configuration - using a simple SMTP service
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>', // Replace with your Gmail
    pass: 'your-app-password'     // Replace with your Gmail App Password
  }
});

// Serve the feedback form
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Feedback Form</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 600px;
                margin: 50px auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .form-container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .form-group {
                margin-bottom: 20px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #555;
            }
            input, textarea {
                width: 100%;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
                box-sizing: border-box;
            }
            textarea {
                height: 120px;
                resize: vertical;
            }
            button {
                background-color: #007bff;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 5px;
                font-size: 16px;
                cursor: pointer;
                width: 100%;
            }
            button:hover {
                background-color: #0056b3;
            }
            button:disabled {
                background-color: #ccc;
                cursor: not-allowed;
            }
            .message {
                padding: 15px;
                margin: 20px 0;
                border-radius: 5px;
                text-align: center;
            }
            .success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .loading {
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
        </style>
    </head>
    <body>
        <div class="form-container">
            <h1>📝 Feedback Form</h1>
            <form id="feedbackForm">
                <div class="form-group">
                    <label for="customerName">Your Name:</label>
                    <input type="text" id="customerName" name="customerName" required 
                           placeholder="Enter your full name">
                </div>
                
                <div class="form-group">
                    <label for="recipientEmail">Send feedback to (Email):</label>
                    <input type="email" id="recipientEmail" name="recipientEmail" required 
                           placeholder="Enter recipient's email (e.g., <EMAIL>)">
                </div>
                
                <div class="form-group">
                    <label for="feedbackContent">Feedback Message:</label>
                    <textarea id="feedbackContent" name="feedbackContent" required 
                              placeholder="Enter your feedback message here..."></textarea>
                </div>
                
                <button type="submit" id="submitBtn">Send Feedback</button>
            </form>
            
            <div id="message" style="display: none;"></div>
        </div>

        <script>
            document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const submitBtn = document.getElementById('submitBtn');
                const messageDiv = document.getElementById('message');
                
                // Get form data
                const formData = {
                    customerName: document.getElementById('customerName').value,
                    recipientEmail: document.getElementById('recipientEmail').value,
                    feedbackContent: document.getElementById('feedbackContent').value
                };
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.textContent = 'Sending...';
                messageDiv.style.display = 'block';
                messageDiv.className = 'message loading';
                messageDiv.textContent = '📤 Sending your feedback...';
                
                try {
                    const response = await fetch('/send-feedback', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        messageDiv.className = 'message success';
                        messageDiv.textContent = '✅ Feedback sent successfully! Email delivered to ' + formData.recipientEmail;
                        document.getElementById('feedbackForm').reset();
                    } else {
                        messageDiv.className = 'message error';
                        messageDiv.textContent = '❌ Failed to send feedback: ' + result.message;
                    }
                } catch (error) {
                    messageDiv.className = 'message error';
                    messageDiv.textContent = '❌ Error sending feedback. Please try again.';
                }
                
                // Reset button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Feedback';
            });
        </script>
    </body>
    </html>
  `);
});

// API endpoint to send feedback
app.post('/send-feedback', async (req, res) => {
  try {
    const { customerName, recipientEmail, feedbackContent } = req.body;
    
    // Validation
    if (!customerName || !recipientEmail || !feedbackContent) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address'
      });
    }
    
    // Create email content
    const mailOptions = {
      from: '<EMAIL>', // Replace with your Gmail
      to: recipientEmail,
      subject: \`📝 New Feedback from \${customerName}\`,
      html: \`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
            📝 New Feedback Received
          </h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p><strong>From:</strong> \${customerName}</p>
            <p><strong>Date:</strong> \${new Date().toLocaleString()}</p>
          </div>
          
          <div style="background-color: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
            <h3 style="color: #555; margin-top: 0;">Feedback Message:</h3>
            <p style="line-height: 1.6; color: #333;">\${feedbackContent}</p>
          </div>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px; text-align: center;">
            This feedback was sent through the PinnacleAi Feedback System
          </p>
        </div>
      \`
    };
    
    // Send email
    const info = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', info.messageId);
    console.log('Feedback sent to:', recipientEmail);
    
    res.json({
      success: true,
      message: 'Feedback sent successfully',
      messageId: info.messageId
    });
    
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send email: ' + error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(\`🚀 Feedback server running at http://localhost:\${PORT}\`);
  console.log('📝 Open your browser and go to the URL above to test the feedback form');
});