const express = require('express');
const nodemailer = require('nodemailer');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Create a test email account using Ethereal (for testing)
// In production, you'd use a real email service
let transporter;

async function createEmailTransporter() {
  try {
    // For demo purposes, we'll use a simple SMTP configuration
    // You can replace this with any email service
    transporter = nodemailer.createTransporter({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>', // Demo email
        pass: 'demo-password' // This would be replaced with real credentials
      }
    });
    
    console.log('📧 Email transporter created');
  } catch (error) {
    console.error('❌ Failed to create email transporter:', error);
  }
}

// Initialize email transporter
createEmailTransporter();

// Serve the feedback form
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Send Feedback</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            
            .container {
                background: white;
                padding: 40px;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                width: 100%;
                max-width: 500px;
            }
            
            h1 {
                text-align: center;
                color: #333;
                margin-bottom: 30px;
                font-size: 28px;
            }
            
            .form-group {
                margin-bottom: 25px;
            }
            
            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #555;
                font-size: 14px;
            }
            
            input, textarea {
                width: 100%;
                padding: 15px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 16px;
                transition: border-color 0.3s ease;
            }
            
            input:focus, textarea:focus {
                outline: none;
                border-color: #667eea;
            }
            
            textarea {
                height: 120px;
                resize: vertical;
                font-family: inherit;
            }
            
            .send-btn {
                width: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.2s ease;
            }
            
            .send-btn:hover {
                transform: translateY(-2px);
            }
            
            .send-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
            }
            
            .message {
                padding: 15px;
                margin: 20px 0;
                border-radius: 8px;
                text-align: center;
                font-weight: 500;
            }
            
            .success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .loading {
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
            
            .example {
                font-size: 12px;
                color: #666;
                margin-top: 5px;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📝 Send Feedback</h1>
            
            <form id="feedbackForm">
                <div class="form-group">
                    <label for="customerName">Your Name</label>
                    <input type="text" id="customerName" required 
                           placeholder="Enter your full name">
                </div>
                
                <div class="form-group">
                    <label for="recipientEmail">Send to Email</label>
                    <input type="email" id="recipientEmail" required 
                           placeholder="Enter recipient's email address">
                    <div class="example">Example: <EMAIL></div>
                </div>
                
                <div class="form-group">
                    <label for="feedbackContent">Your Feedback</label>
                    <textarea id="feedbackContent" required 
                              placeholder="Write your feedback message here..."></textarea>
                </div>
                
                <button type="submit" class="send-btn" id="submitBtn">
                    Send Feedback
                </button>
            </form>
            
            <div id="message" style="display: none;"></div>
        </div>

        <script>
            document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const submitBtn = document.getElementById('submitBtn');
                const messageDiv = document.getElementById('message');
                
                const formData = {
                    customerName: document.getElementById('customerName').value.trim(),
                    recipientEmail: document.getElementById('recipientEmail').value.trim(),
                    feedbackContent: document.getElementById('feedbackContent').value.trim()
                };
                
                // Validation
                if (!formData.customerName || !formData.recipientEmail || !formData.feedbackContent) {
                    showMessage('Please fill in all fields', 'error');
                    return;
                }
                
                // Show loading
                submitBtn.disabled = true;
                submitBtn.textContent = 'Sending...';
                showMessage('📤 Sending your feedback...', 'loading');
                
                try {
                    const response = await fetch('/send-feedback', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showMessage(\`✅ Feedback sent successfully to \${formData.recipientEmail}!\`, 'success');
                        document.getElementById('feedbackForm').reset();
                    } else {
                        showMessage(\`❌ Failed to send: \${result.message}\`, 'error');
                    }
                } catch (error) {
                    showMessage('❌ Network error. Please try again.', 'error');
                }
                
                // Reset button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Feedback';
            });
            
            function showMessage(text, type) {
                const messageDiv = document.getElementById('message');
                messageDiv.style.display = 'block';
                messageDiv.className = \`message \${type}\`;
                messageDiv.textContent = text;
            }
        </script>
    </body>
    </html>
  `);
});

// API endpoint to send feedback
app.post('/send-feedback', async (req, res) => {
  try {
    const { customerName, recipientEmail, feedbackContent } = req.body;
    
    console.log('📝 Feedback received:', {
      from: customerName,
      to: recipientEmail,
      message: feedbackContent.substring(0, 50) + '...'
    });
    
    // For now, we'll simulate email sending since we don't have real email credentials
    // In a real implementation, you would configure proper email credentials
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Log the email that would be sent
    console.log('📧 Email would be sent:');
    console.log('To:', recipientEmail);
    console.log('From:', customerName);
    console.log('Subject: New Feedback from', customerName);
    console.log('Content:', feedbackContent);
    console.log('---');
    
    // For demo purposes, always return success
    // In production, this would actually send the email
    res.json({
      success: true,
      message: 'Feedback sent successfully',
      note: 'This is a demo - in production, real emails would be sent'
    });
    
  } catch (error) {
    console.error('❌ Error processing feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Server error occurred'
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(\`🚀 Feedback server running at http://localhost:\${PORT}\`);
  console.log('📝 Open your browser and visit the URL above');
  console.log('');
  console.log('📧 Note: This is currently in demo mode');
  console.log('   To send real emails, configure proper email credentials');
});