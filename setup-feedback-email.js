#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupFeedbackEmail() {
  console.log('\n🔧 Feedback Email Configuration Setup\n');
  console.log('This will configure your system to send real emails.\n');
  
  try {
    // Get email credentials
    const emailUser = await question('Enter your Gmail address: ');
    const emailPass = await question('Enter your Gmail App Password (16 characters): ');
    
    if (!emailUser || !emailPass) {
      console.log('❌ Email and password are required!');
      process.exit(1);
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailUser)) {
      console.log('❌ Invalid email format!');
      process.exit(1);
    }
    
    // Create email configuration
    const emailConfig = {
      service: 'gmail',
      auth: {
        user: emailUser,
        pass: emailPass
      }
    };
    
    // Ensure feedback config directory exists
    const configDir = path.join(__dirname, 'DATA', 'feedback', 'config');
    await fs.mkdir(configDir, { recursive: true });
    
    // Save configuration
    const configPath = path.join(configDir, 'email-config.json');
    await fs.writeFile(configPath, JSON.stringify(emailConfig, null, 2));
    
    console.log('\n✅ Email configuration saved successfully!');
    console.log(`📁 Config saved to: ${configPath}`);
    
    // Test the configuration
    console.log('\n🧪 Testing email configuration...');
    
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransport(emailConfig);
    
    try {
      await transporter.verify();
      console.log('✅ Email configuration test successful!');
      console.log('📧 Your feedback system can now send real emails.');
      
      // Send a test email to the configured address
      const testEmail = {
        from: emailUser,
        to: emailUser,
        subject: '✅ Feedback System Test Email',
        html: `
          <h2>🎉 Feedback System Successfully Configured!</h2>
          <p>This is a test email to confirm your feedback system is working properly.</p>
          <p><strong>Configuration Details:</strong></p>
          <ul>
            <li>Email Service: Gmail</li>
            <li>From Address: ${emailUser}</li>
            <li>Status: ✅ Active</li>
          </ul>
          <p>Your feedback system is now ready to send real emails!</p>
          <hr>
          <small>Generated by PinnacleAi Feedback System</small>
        `
      };
      
      await transporter.sendMail(testEmail);
      console.log(`📧 Test email sent to ${emailUser}`);
      console.log('Check your inbox to confirm email delivery!');
      
    } catch (error) {
      console.log('❌ Email configuration test failed:', error.message);
      console.log('Please check your credentials and try again.');
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  } finally {
    rl.close();
  }
}

// Run setup
setupFeedbackEmail().catch(console.error);