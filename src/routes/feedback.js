const express = require('express');
const router = express.Router();
const path = require('path');

// Import the feedback backend service
const FeedbackBackendService = require('../../server/feedback/feedbackBackendService.js');

// Initialize the feedback service
const feedbackService = new FeedbackBackendService();

/**
 * POST /api/feedback/submit
 * Submit feedback and send email
 */
router.post('/submit', async (req, res) => {
  try {
    const { customerName, customerEmail, recipientEmail, feedbackContent } = req.body;

    // Validation
    if (!customerName || !recipientEmail || !feedbackContent) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: customerName, recipientEmail, feedbackContent',
        emailSent: false
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid recipient email address',
        emailSent: false
      });
    }

    if (customerEmail && !emailRegex.test(customerEmail)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid customer email address',
        emailSent: false
      });
    }

    // Submit feedback using the backend service
    const result = await feedbackService.submitFeedback({
      customerName,
      customerEmail: customerEmail || '<EMAIL>',
      recipientEmail,
      feedbackContent
    });

    res.json(result);

  } catch (error) {
    console.error('Error submitting feedback:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to submit feedback',
      emailSent: false
    });
  }
});

/**
 * GET /api/feedback/dashboard/:timeRange
 * Get dashboard data
 */
router.get('/dashboard/:timeRange?', async (req, res) => {
  try {
    const timeRange = req.params.timeRange || '7d';
    
    if (!['1d', '7d', '30d'].includes(timeRange)) {
      return res.status(400).json({
        error: 'Invalid time range. Must be 1d, 7d, or 30d'
      });
    }

    const dashboardData = await feedbackService.getDashboardData(timeRange);
    res.json(dashboardData);

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      error: error.message || 'Failed to fetch dashboard data'
    });
  }
});

/**
 * POST /api/feedback/email-config
 * Update email configuration
 */
router.post('/email-config', async (req, res) => {
  try {
    const { service, user, pass } = req.body;

    if (!service || !user || !pass) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: service, user, pass'
      });
    }

    const result = await feedbackService.updateEmailConfig({
      service,
      user,
      pass
    });

    res.json(result);

  } catch (error) {
    console.error('Error updating email config:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update email configuration'
    });
  }
});

/**
 * GET /api/feedback/email-config
 * Get email configuration status
 */
router.get('/email-config', async (req, res) => {
  try {
    const config = await feedbackService.getEmailConfig();
    res.json(config);

  } catch (error) {
    console.error('Error fetching email config:', error);
    res.status(500).json({
      error: error.message || 'Failed to fetch email configuration'
    });
  }
});

module.exports = router;