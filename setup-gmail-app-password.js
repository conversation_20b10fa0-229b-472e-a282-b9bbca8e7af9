#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupGmailAppPassword() {
  console.log('🔧 Gmail App Password Setup for Feedback System');
  console.log('================================================\n');
  
  console.log('📋 IMPORTANT: You need to set up a Gmail App Password');
  console.log('   Regular Gmail passwords won\'t work for security reasons.\n');
  
  console.log('📝 Steps to create a Gmail App Password:');
  console.log('   1. Go to https://myaccount.google.com/security');
  console.log('   2. Enable 2-Factor Authentication (if not already enabled)');
  console.log('   3. Go to https://myaccount.google.com/apppasswords');
  console.log('   4. Select "Mail" and "Other (custom name)"');
  console.log('   5. Enter "PinnacleAi Feedback System" as the name');
  console.log('   6. Copy the 16-character App Password\n');
  
  const proceed = await question('Have you created a Gmail App Password? (y/n): ');
  
  if (proceed.toLowerCase() !== 'y') {
    console.log('\n❌ Please create a Gmail App Password first and run this script again.');
    process.exit(1);
  }
  
  try {
    const emailUser = await question('\nEnter your Gmail address: ');
    const appPassword = await question('Enter your 16-character Gmail App Password: ');
    
    if (!emailUser || !appPassword) {
      console.log('❌ Both email and app password are required!');
      process.exit(1);
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailUser)) {
      console.log('❌ Invalid email format!');
      process.exit(1);
    }
    
    // Validate app password format (16 characters, no spaces)
    const cleanAppPassword = appPassword.replace(/\s/g, '');
    if (cleanAppPassword.length !== 16) {
      console.log('❌ Gmail App Password should be exactly 16 characters!');
      console.log('   Make sure to copy the entire password without spaces.');
      process.exit(1);
    }
    
    // Create email configuration
    const emailConfig = {
      service: 'gmail',
      auth: {
        user: emailUser,
        pass: cleanAppPassword
      }
    };
    
    // Ensure feedback config directory exists
    const configDir = path.join(__dirname, 'DATA', 'feedback', 'config');
    await fs.mkdir(configDir, { recursive: true });
    
    // Save configuration
    const configPath = path.join(configDir, 'email-config.json');
    await fs.writeFile(configPath, JSON.stringify(emailConfig, null, 2));
    
    console.log('\n✅ Gmail App Password configuration saved successfully!');
    console.log(`📁 Config saved to: ${configPath}`);
    
    // Test the configuration
    console.log('\n🧪 Testing Gmail configuration...');
    
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransporter(emailConfig);
    
    try {
      await transporter.verify();
      console.log('✅ Gmail connection test successful!');
      console.log('📧 Your feedback system can now send real emails.');
      
      // Send a test email to the configured address
      const testEmail = {
        from: emailUser,
        to: emailUser,
        subject: '✅ PinnacleAi Feedback System - Gmail Setup Complete',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">
              🎉 Gmail Setup Successful!
            </h2>
            <p>Your PinnacleAi Feedback System is now configured to send real emails through Gmail.</p>
            
            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1e293b; margin-top: 0;">Configuration Details:</h3>
              <ul>
                <li><strong>Email Service:</strong> Gmail</li>
                <li><strong>From Address:</strong> ${emailUser}</li>
                <li><strong>Authentication:</strong> App Password ✅</li>
                <li><strong>Status:</strong> Active and Ready</li>
              </ul>
            </div>
            
            <div style="background-color: #dcfce7; padding: 15px; border-radius: 8px; border-left: 4px solid #16a34a;">
              <p style="margin: 0; color: #166534;">
                <strong>✅ Test Successful:</strong> Your feedback system is now ready to send real emails to recipients!
              </p>
            </div>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e2e8f0;">
            <p style="color: #64748b; font-size: 12px; text-align: center;">
              Generated by PinnacleAi Feedback System Setup
            </p>
          </div>
        `
      };
      
      await transporter.sendMail(testEmail);
      console.log(`📧 Test email sent to ${emailUser}`);
      console.log('✅ Check your Gmail inbox to confirm email delivery!');
      console.log('\n🎯 Next Steps:');
      console.log('   1. Restart your PinnacleAi application');
      console.log('   2. Test the feedback form - emails will now be delivered to real recipients');
      console.log('   3. Check the feedback dashboard for delivery statistics');
      
    } catch (error) {
      console.log('❌ Gmail configuration test failed:', error.message);
      
      if (error.code === 'EAUTH') {
        console.log('\n🔑 Authentication Error - Please check:');
        console.log('   1. Make sure you\'re using an App Password, not your regular Gmail password');
        console.log('   2. Ensure 2-Factor Authentication is enabled on your Google account');
        console.log('   3. Generate a new App Password from: https://myaccount.google.com/apppasswords');
        console.log('   4. Make sure the App Password is exactly 16 characters');
      } else if (error.code === 'ENOTFOUND') {
        console.log('\n🌐 Network Error - Please check your internet connection');
      } else {
        console.log('\n❓ Unexpected error. Please try again or check your configuration.');
      }
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  } finally {
    rl.close();
  }
}

// Run setup
setupGmailAppPassword().catch(console.error);
