#!/usr/bin/env node

const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

async function testFeedbackEmail() {
  console.log('🧪 Testing Feedback Email System');
  console.log('================================\n');
  
  try {
    // Load the email configuration
    const configPath = path.join(__dirname, 'DATA', 'feedback', 'config', 'email-config.json');
    
    if (!fs.existsSync(configPath)) {
      console.log('❌ Email configuration not found!');
      console.log('📝 Please run: node setup-gmail-app-password.js');
      return;
    }
    
    const configData = fs.readFileSync(configPath, 'utf8');
    const emailConfig = JSON.parse(configData);
    
    console.log('📧 Testing email configuration:');
    console.log(`   Service: ${emailConfig.service}`);
    console.log(`   User: ${emailConfig.auth.user}`);
    console.log(`   Password: ${'*'.repeat(emailConfig.auth.pass.length)} (${emailConfig.auth.pass.length} chars)\n`);
    
    // Create transporter
    const transporter = nodemailer.createTransport(emailConfig);
    
    // Test connection
    console.log('🔗 Testing Gmail connection...');
    await transporter.verify();
    console.log('✅ Gmail connection successful!\n');
    
    // Send test feedback email
    console.log('📤 Sending test feedback email...');
    
    const testFeedback = {
      customerName: 'Test User',
      customerEmail: '<EMAIL>',
      recipientEmail: '<EMAIL>', // Your target email
      feedbackContent: `This is a test feedback email to verify that the PinnacleAi Feedback System is working correctly.

The system should now be able to send real emails to recipients instead of just showing "Email sent successfully" messages.

Test Details:
- Timestamp: ${new Date().toLocaleString()}
- System: PinnacleAi Feedback System
- Configuration: Gmail with App Password
- Status: Testing Real Email Delivery

If you receive this email, the feedback system is working perfectly! 🎉`
    };
    
    const mailOptions = {
      from: `"${testFeedback.customerName} via PinnacleAi Feedback" <${emailConfig.auth.user}>`,
      to: testFeedback.recipientEmail,
      subject: `📝 Test Feedback from ${testFeedback.customerName}`,
      replyTo: testFeedback.customerEmail,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">
            📝 Test Feedback Received
          </h2>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e293b; margin-top: 0;">Customer Information</h3>
            <p><strong>Name:</strong> ${testFeedback.customerName}</p>
            <p><strong>Email:</strong> ${testFeedback.customerEmail}</p>
            <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
          </div>
          
          <div style="background-color: #ffffff; padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
            <h3 style="color: #1e293b; margin-top: 0;">Feedback Message</h3>
            <p style="line-height: 1.6; color: #475569;">${testFeedback.feedbackContent.replace(/\n/g, '<br>')}</p>
          </div>
          
          <div style="margin-top: 20px; padding: 15px; background-color: #dcfce7; border-radius: 8px; border-left: 4px solid #16a34a;">
            <p style="margin: 0; color: #166534; font-size: 14px;">
              <strong>✅ Success:</strong> This is a test email to verify that your feedback system is working correctly.<br>
              <strong>System:</strong> PinnacleAi Feedback System<br>
              <strong>Configuration:</strong> Gmail with App Password
            </p>
          </div>
        </div>
      `,
      text: `
Test Feedback Received

Customer: ${testFeedback.customerName}
Email: ${testFeedback.customerEmail}
Submitted: ${new Date().toLocaleString()}

Message:
${testFeedback.feedbackContent}

This is a test email to verify that your feedback system is working correctly.
      `
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    console.log('✅ Test email sent successfully!');
    console.log(`📧 Message ID: ${info.messageId}`);
    console.log(`📧 Sent to: ${testFeedback.recipientEmail}`);
    console.log(`📧 From: ${emailConfig.auth.user}`);
    console.log(`📧 Subject: ${mailOptions.subject}\n`);
    
    console.log('🎯 Next Steps:');
    console.log(`   1. Check the inbox at ${testFeedback.recipientEmail}`);
    console.log('   2. If you received the email, your feedback system is working!');
    console.log('   3. If not, check spam folder or run setup-gmail-app-password.js again');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'EAUTH') {
      console.log('\n🔑 Authentication Error:');
      console.log('   Your Gmail credentials are incorrect or expired.');
      console.log('   Please run: node setup-gmail-app-password.js');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n🌐 Network Error:');
      console.log('   Please check your internet connection.');
    } else {
      console.log('\n❓ Unexpected error occurred.');
      console.log('   Please check your configuration and try again.');
    }
  }
}

// Run test
testFeedbackEmail().catch(console.error);
