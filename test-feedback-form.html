<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Feedback Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            opacity: 0.9;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>🧪 Test Feedback Form</h1>
        <p>This form will test the feedback email system directly.</p>
        
        <div id="message" class="message"></div>
        
        <form id="feedbackForm">
            <div class="form-group">
                <label for="customerName">Your Name *</label>
                <input type="text" id="customerName" name="customerName" value="Test User" required>
            </div>
            
            <div class="form-group">
                <label for="customerEmail">Your Email (Optional)</label>
                <input type="email" id="customerEmail" name="customerEmail" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="recipientEmail">Send Feedback To *</label>
                <input type="email" id="recipientEmail" name="recipientEmail" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="feedbackContent">Your Feedback *</label>
                <textarea id="feedbackContent" name="feedbackContent" required>This is a test feedback to verify that the email system is working correctly.

The system should now send real emails to the recipient instead of just showing "Email sent successfully" messages.

Test Details:
- Timestamp: [Will be filled automatically]
- System: PinnacleAi Feedback System  
- Configuration: Updated Backend Service
- Expected Result: Real email delivery

If you receive this email, the feedback system is working perfectly! 🎉</textarea>
            </div>
            
            <button type="submit" id="submitBtn">Send Test Feedback</button>
        </form>
    </div>

    <script>
        document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            
            // Get form data
            const formData = {
                customerName: document.getElementById('customerName').value,
                customerEmail: document.getElementById('customerEmail').value,
                recipientEmail: document.getElementById('recipientEmail').value,
                feedbackContent: document.getElementById('feedbackContent').value.replace('[Will be filled automatically]', new Date().toLocaleString())
            };
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = 'Sending...';
            showMessage('Submitting feedback...', 'info');
            
            try {
                const response = await fetch('/api/feedback/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage(`✅ Success! ${result.message}${result.emailSent ? ' Email sent to recipient.' : ' Email delivery pending.'}`, 'success');
                    
                    // Reset form
                    document.getElementById('feedbackContent').value = document.getElementById('feedbackContent').value.split('Test Details:')[0] + 'Test Details:\n- Timestamp: [Will be filled automatically]\n- System: PinnacleAi Feedback System  \n- Configuration: Updated Backend Service\n- Expected Result: Real email delivery\n\nIf you receive this email, the feedback system is working perfectly! 🎉';
                } else {
                    showMessage(`❌ Error: ${result.message || 'Failed to submit feedback'}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Test Feedback';
            }
        });
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            // Auto-hide after 10 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 10000);
            }
        }
    </script>
</body>
</html>
