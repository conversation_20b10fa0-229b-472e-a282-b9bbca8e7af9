const fetch = require('node-fetch');

async function testFeedbackAPI() {
  try {
    console.log('🧪 Testing Feedback API...\n');
    
    const feedbackData = {
      customerName: 'Test User',
      recipientEmail: '<EMAIL>',
      feedbackContent: 'This is a test feedback message to verify that the email system is working correctly. The user should receive this email in their inbox.',
      customerEmail: '<EMAIL>'
    };
    
    console.log('📤 Sending feedback:', {
      from: feedbackData.customerName,
      to: feedbackData.recipientEmail,
      message: feedbackData.feedbackContent.substring(0, 50) + '...'
    });
    
    const response = await fetch('http://localhost:5641/api/feedback/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(feedbackData)
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('\n✅ API Test Successful!');
      console.log('📧 Email sent:', result.emailSent);
      console.log('📝 Feedback ID:', result.feedbackId);
      console.log('💬 Message:', result.message);
      console.log('\n🎉 Your feedback system is now working!');
      console.log('📧 Real emails will be sent when users submit feedback.');
    } else {
      console.log('\n❌ API Test Failed:');
      console.log('Status:', response.status);
      console.log('Response:', result);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFeedbackAPI();