const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function updateEmailConfig() {
  console.log('\n🔑 Update Gmail App Password\n');
  console.log('You need to use a Gmail App Password, not your regular password.\n');
  
  try {
    const appPassword = await question('Enter your Gmail App Password (16 characters): ');
    
    if (!appPassword || appPassword.length < 16) {
      console.log('❌ App Password should be 16 characters long!');
      console.log('Get it from: https://myaccount.google.com/apppasswords');
      process.exit(1);
    }
    
    // Update the configuration
    const configPath = path.join(__dirname, 'DATA', 'feedback', 'config', 'email-config.json');
    const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    configData.auth.pass = appPassword;
    
    fs.writeFileSync(configPath, JSON.stringify(configData, null, 2));
    
    console.log('\n✅ Email configuration updated!');
    console.log('🧪 Testing new configuration...\n');
    
    // Test the new configuration
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransport(configData);
    
    await transporter.verify();
    console.log('✅ Email server connection successful!');
    
    // Send test email
    const testEmail = {
      from: configData.auth.user,
      to: '<EMAIL>',
      subject: '🎉 Feedback System - Email Configuration Fixed!',
      html: `
        <h2>🎉 Success! Your Email Configuration is Now Working</h2>
        <p>This email confirms that your feedback system can now send real emails.</p>
        <p><strong>What's Fixed:</strong></p>
        <ul>
          <li>✅ Gmail App Password configured correctly</li>
          <li>✅ Email authentication working</li>
          <li>✅ Feedback system ready to send emails</li>
        </ul>
        <p>🚀 <strong>Try it now:</strong></p>
        <ol>
          <li>Go to your feedback form</li>
          <li>Submit feedback to any email address</li>
          <li>The recipient will receive it immediately!</li>
        </ol>
        <hr>
        <small>PinnacleAi Feedback System - ${new Date().toLocaleString()}</small>
      `
    };
    
    const result = await transporter.sendMail(testEmail);
    console.log('📧 Test email sent successfully!');
    console.log('✅ Check your inbox - the feedback system is now working!');
    
  } catch (error) {
    console.error('❌ Configuration update failed:', error.message);
  } finally {
    rl.close();
  }
}

updateEmailConfig();