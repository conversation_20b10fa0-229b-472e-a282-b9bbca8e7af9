const nodemailer = require('nodemailer');
const fs = require('fs');
const path = require('path');

async function testEmail() {
  try {
    // Load the saved configuration
    const configPath = path.join(__dirname, 'DATA', 'feedback', 'config', 'email-config.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    const emailConfig = JSON.parse(configData);
    
    console.log('📧 Testing email with configuration:', {
      service: emailConfig.service,
      user: emailConfig.auth.user
    });
    
    // Create transporter
    const transporter = nodemailer.createTransport(emailConfig);
    
    // Test connection
    await transporter.verify();
    console.log('✅ Email server connection successful!');
    
    // Send test email
    const testEmail = {
      from: emailConfig.auth.user,
      to: '<EMAIL>',
      subject: '🎉 Feedback System Test - Email Working!',
      html: `
        <h2>✅ Success! Your Feedback System is Now Sending Real Emails</h2>
        <p>This test email confirms that your feedback system is properly configured and working.</p>
        <p><strong>Configuration Details:</strong></p>
        <ul>
          <li>✅ Email Service: Gmail</li>
          <li>✅ From Address: ${emailConfig.auth.user}</li>
          <li>✅ Status: Active and Working</li>
        </ul>
        <p>🎯 <strong>Next Steps:</strong></p>
        <ol>
          <li>Go back to your feedback form</li>
          <li>Submit a new feedback</li>
          <li>You should receive it within seconds!</li>
        </ol>
        <hr>
        <small>Generated by PinnacleAi Feedback System - ${new Date().toLocaleString()}</small>
      `
    };
    
    const result = await transporter.sendMail(testEmail);
    console.log('📧 Test email sent successfully!');
    console.log('Message ID:', result.messageId);
    console.log('✅ Check your <NAME_EMAIL>');
    
  } catch (error) {
    console.error('❌ Email test failed:', error.message);
    
    if (error.code === 'EAUTH') {
      console.log('\n🔑 Authentication Error - Please check:');
      console.log('1. Make sure you\'re using an App Password, not your regular Gmail password');
      console.log('2. Enable 2-Factor Authentication on your Google account');
      console.log('3. Generate a new App Password from: https://myaccount.google.com/apppasswords');
    }
  }
}

testEmail();