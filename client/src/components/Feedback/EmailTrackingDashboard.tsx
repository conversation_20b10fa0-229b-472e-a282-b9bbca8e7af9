import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import advancedAnalyticsService, { EmailTrackingData } from './advancedAnalyticsService';

interface EmailTrackingMetrics {
  totalEmails: number;
  deliveredEmails: number;
  openedEmails: number;
  clickedEmails: number;
  bouncedEmails: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
}

const EmailTrackingDashboard: React.FC<{
  timeRange: '1d' | '7d' | '30d' | '90d';
}> = ({ timeRange }) => {
  const { currentTheme } = useTheme();
  const [trackingData, setTrackingData] = useState<EmailTrackingData[]>([]);
  const [metrics, setMetrics] = useState<EmailTrackingMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmail, setSelectedEmail] = useState<EmailTrackingData | null>(null);

  useEffect(() => {
    loadTrackingData();
  }, [timeRange]);

  const loadTrackingData = async () => {
    try {
      setLoading(true);
      setError(null);
      const analyticsData = await advancedAnalyticsService.getAdvancedAnalytics(timeRange);
      setTrackingData(analyticsData.emailTracking);
      setMetrics(calculateMetrics(analyticsData.emailTracking));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load tracking data');
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = (data: EmailTrackingData[]): EmailTrackingMetrics => {
    const totalEmails = data.length;
    const deliveredEmails = data.filter(d => d.delivered).length;
    const openedEmails = data.filter(d => d.opened).length;
    const clickedEmails = data.filter(d => d.clicked).length;
    const bouncedEmails = data.filter(d => d.bounced).length;

    return {
      totalEmails,
      deliveredEmails,
      openedEmails,
      clickedEmails,
      bouncedEmails,
      deliveryRate: totalEmails > 0 ? (deliveredEmails / totalEmails) * 100 : 0,
      openRate: deliveredEmails > 0 ? (openedEmails / deliveredEmails) * 100 : 0,
      clickRate: openedEmails > 0 ? (clickedEmails / openedEmails) * 100 : 0,
      bounceRate: totalEmails > 0 ? (bouncedEmails / totalEmails) * 100 : 0
    };
  };

  const formatTimestamp = (timestamp: string | undefined) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced') => {
    const colors = {
      sent: 'var(--color-info)',
      delivered: 'var(--color-success)',
      opened: 'var(--color-primary)',
      clicked: 'var(--color-secondary)',
      bounced: 'var(--color-error)'
    };
    return colors[status];
  };

  const getStatusIcon = (status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced') => {
    const icons = {
      sent: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
        </svg>
      ),
      delivered: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      ),
      opened: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      ),
      clicked: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
        </svg>
      ),
      bounced: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    };
    return icons[status];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <svg className="animate-spin w-8 h-8 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p style={{ color: 'var(--color-text-muted)' }}>Loading email tracking data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="p-6 rounded-lg border" style={{
          backgroundColor: 'var(--color-error)20',
          borderColor: 'var(--color-error)',
          color: 'var(--color-error)'
        }}>
          <h3 className="text-lg font-semibold mb-2">Error Loading Email Tracking</h3>
          <p className="mb-4">{error}</p>
          <button
            onClick={loadTrackingData}
            className="px-4 py-2 rounded-lg font-medium"
            style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Email Tracking Metrics */}
      {metrics && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="p-4 rounded-lg border" style={{
            backgroundColor: 'var(--color-surface)',
            borderColor: 'var(--color-border)'
          }}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                  Delivery Rate
                </p>
                <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                  {metrics.deliveryRate.toFixed(1)}%
                </p>
              </div>
              <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-success)20' }}>
                {getStatusIcon('delivered')}
              </div>
            </div>
          </div>

          <div className="p-4 rounded-lg border" style={{
            backgroundColor: 'var(--color-surface)',
            borderColor: 'var(--color-border)'
          }}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                  Open Rate
                </p>
                <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                  {metrics.openRate.toFixed(1)}%
                </p>
              </div>
              <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-primary)20' }}>
                {getStatusIcon('opened')}
              </div>
            </div>
          </div>

          <div className="p-4 rounded-lg border" style={{
            backgroundColor: 'var(--color-surface)',
            borderColor: 'var(--color-border)'
          }}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                  Click Rate
                </p>
                <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                  {metrics.clickRate.toFixed(1)}%
                </p>
              </div>
              <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-secondary)20' }}>
                {getStatusIcon('clicked')}
              </div>
            </div>
          </div>

          <div className="p-4 rounded-lg border" style={{
            backgroundColor: 'var(--color-surface)',
            borderColor: 'var(--color-border)'
          }}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                  Bounce Rate
                </p>
                <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                  {metrics.bounceRate.toFixed(1)}%
                </p>
              </div>
              <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-error)20' }}>
                {getStatusIcon('bounced')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Email Tracking Table */}
      <div className="rounded-xl shadow-lg overflow-hidden" style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}>
        <div className="p-6 border-b" style={{ borderColor: 'var(--color-border)' }}>
          <h2 className="text-xl font-semibold" style={{ color: 'var(--color-text)' }}>
            Email Tracking Details
          </h2>
          <p className="text-sm mt-1" style={{ color: 'var(--color-text-muted)' }}>
            Track the delivery and engagement status of feedback emails
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b" style={{ borderColor: 'var(--color-border)' }}>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Recipient
                </th>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Status
                </th>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Sent
                </th>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Delivered
                </th>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Opened
                </th>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Clicked
                </th>
                <th className="text-left p-4 font-medium" style={{ color: 'var(--color-text)' }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {trackingData.map((email) => (
                <tr 
                  key={email.messageId}
                  className="border-b hover:bg-opacity-50 transition-colors cursor-pointer"
                  style={{ borderColor: 'var(--color-border)' }}
                  onClick={() => setSelectedEmail(email)}
                >
                  <td className="p-4">
                    <div>
                      <p className="font-medium" style={{ color: 'var(--color-text)' }}>
                        {email.recipientInfo.email}
                      </p>
                      <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                        {email.recipientInfo.provider} • {email.recipientInfo.location}
                      </p>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center space-x-2">
                      {email.bounced ? (
                        <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                              style={{ backgroundColor: 'var(--color-error)20', color: 'var(--color-error)' }}>
                          {getStatusIcon('bounced')}
                          <span>Bounced</span>
                        </span>
                      ) : email.clicked ? (
                        <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                              style={{ backgroundColor: 'var(--color-secondary)20', color: 'var(--color-secondary)' }}>
                          {getStatusIcon('clicked')}
                          <span>Clicked</span>
                        </span>
                      ) : email.opened ? (
                        <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                              style={{ backgroundColor: 'var(--color-primary)20', color: 'var(--color-primary)' }}>
                          {getStatusIcon('opened')}
                          <span>Opened</span>
                        </span>
                      ) : email.delivered ? (
                        <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                              style={{ backgroundColor: 'var(--color-success)20', color: 'var(--color-success)' }}>
                          {getStatusIcon('delivered')}
                          <span>Delivered</span>
                        </span>
                      ) : email.sent ? (
                        <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                              style={{ backgroundColor: 'var(--color-info)20', color: 'var(--color-info)' }}>
                          {getStatusIcon('sent')}
                          <span>Sent</span>
                        </span>
                      ) : (
                        <span className="flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium"
                              style={{ backgroundColor: 'var(--color-text-muted)20', color: 'var(--color-text-muted)' }}>
                          <span>Pending</span>
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="p-4 text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    {formatTimestamp(email.timestamps.sent)}
                  </td>
                  <td className="p-4 text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    {formatTimestamp(email.timestamps.delivered)}
                  </td>
                  <td className="p-4 text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    {formatTimestamp(email.timestamps.opened)}
                  </td>
                  <td className="p-4 text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    {formatTimestamp(email.timestamps.clicked)}
                  </td>
                  <td className="p-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedEmail(email);
                      }}
                      className="text-sm px-3 py-1 rounded-lg transition-colors"
                      style={{
                        backgroundColor: 'var(--color-primary)20',
                        color: 'var(--color-primary)'
                      }}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {trackingData.length === 0 && (
          <div className="text-center py-12">
            <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <p className="text-lg font-medium mb-2" style={{ color: 'var(--color-text)' }}>
              No email tracking data
            </p>
            <p style={{ color: 'var(--color-text-muted)' }}>
              Email tracking data will appear here once emails are sent
            </p>
          </div>
        )}
      </div>

      {/* Email Detail Modal */}
      {selectedEmail && (
        <div className="fixed inset-0 flex items-center justify-center z-50" style={{
          background: 'rgba(0, 0, 0, 0.6)',
          backdropFilter: 'blur(8px)'
        }}>
          <div className="rounded-2xl p-8 w-full max-w-2xl mx-4 shadow-2xl" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '2px solid var(--color-border)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
          }}>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                Email Tracking Details
              </h2>
              <button
                onClick={() => setSelectedEmail(null)}
                className="p-2 rounded-full transition-colors"
                style={{
                  color: 'var(--color-text-muted)',
                  background: 'rgba(0, 0, 0, 0.05)',
                  border: '1px solid var(--color-border)'
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-6">
              {/* Email Info */}
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: 'var(--color-text)' }}>
                  Email Information
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                      Message ID
                    </p>
                    <p className="font-mono text-sm" style={{ color: 'var(--color-text)' }}>
                      {selectedEmail.messageId}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                      Feedback ID
                    </p>
                    <p className="font-mono text-sm" style={{ color: 'var(--color-text)' }}>
                      {selectedEmail.feedbackId}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                      Recipient
                    </p>
                    <p className="text-sm" style={{ color: 'var(--color-text)' }}>
                      {selectedEmail.recipientInfo.email}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                      Provider
                    </p>
                    <p className="text-sm" style={{ color: 'var(--color-text)' }}>
                      {selectedEmail.recipientInfo.provider}
                    </p>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div>
                <h3 className="text-lg font-semibold mb-3" style={{ color: 'var(--color-text)' }}>
                  Email Timeline
                </h3>
                <div className="space-y-3">
                  {[
                    { status: 'sent', label: 'Sent', timestamp: selectedEmail.timestamps.sent, active: selectedEmail.sent },
                    { status: 'delivered', label: 'Delivered', timestamp: selectedEmail.timestamps.delivered, active: selectedEmail.delivered },
                    { status: 'opened', label: 'Opened', timestamp: selectedEmail.timestamps.opened, active: selectedEmail.opened },
                    { status: 'clicked', label: 'Clicked', timestamp: selectedEmail.timestamps.clicked, active: selectedEmail.clicked },
                    { status: 'bounced', label: 'Bounced', timestamp: selectedEmail.timestamps.bounced, active: selectedEmail.bounced }
                  ].map((item) => (
                    <div key={item.status} className={`flex items-center space-x-3 p-3 rounded-lg ${
                      item.active ? 'opacity-100' : 'opacity-50'
                    }`} style={{
                      backgroundColor: item.active ? `${getStatusColor(item.status as any)}20` : 'var(--color-surface)',
                      border: `1px solid ${item.active ? getStatusColor(item.status as any) : 'var(--color-border)'}`
                    }}>
                      <div style={{ color: getStatusColor(item.status as any) }}>
                        {getStatusIcon(item.status as any)}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium" style={{ color: 'var(--color-text)' }}>
                          {item.label}
                        </p>
                        <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                          {item.timestamp ? formatTimestamp(item.timestamp) : 'Not yet'}
                        </p>
                      </div>
                      {item.active && (
                        <svg className="w-5 h-5" style={{ color: getStatusColor(item.status as any) }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailTrackingDashboard;