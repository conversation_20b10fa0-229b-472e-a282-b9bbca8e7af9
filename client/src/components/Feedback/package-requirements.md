# Feedback System Package Requirements

## Required Dependencies

### Backend Dependencies (Node.js)
Add these to your main `package.json`:

```json
{
  "dependencies": {
    "nodemailer": "^6.9.7",
    "uuid": "^9.0.1"
  }
}
```

### Installation Commands
```bash
# Install backend dependencies
npm install nodemailer uuid

# Or using yarn
yarn add nodemailer uuid
```

## Environment Variables

Create or update your `.env` file with email configuration:

```env
# Feedback System Email Configuration
FEEDBACK_EMAIL_USER=<EMAIL>
FEEDBACK_EMAIL_PASS=your-app-password

# For Gmail, you need to:
# 1. Enable 2-factor authentication
# 2. Generate an "App Password" 
# 3. Use the app password (not your regular password)
```

## Gmail Setup Instructions

1. **Enable 2-Factor Authentication**
   - Go to Google Account settings
   - Security → 2-Step Verification → Turn on

2. **Generate App Password**
   - Security → 2-Step Verification → App passwords
   - Select "Mail" and your device
   - Copy the generated 16-character password

3. **Update Environment Variables**
   - Use your Gmail address for `FEEDBACK_EMAIL_USER`
   - Use the app password for `FEEDBACK_EMAIL_PASS`

## File Structure Created

```
/client/src/components/Feedback/
├── feedbackBackendService.js    # Backend email service
├── feedbackService.ts           # Frontend service
├── FeedbackForm.tsx            # Customer feedback form
├── FeedbackDashboard.tsx       # Analytics dashboard
├── FeedbackPage.tsx            # Main page component
├── index.ts                    # Export file
└── package-requirements.md     # This file
```

## Data Storage

The system creates the following directory structure for file-based storage:

```
/DATA/feedback/
├── daily/
│   ├── 2024-01-15-submissions.json
│   └── 2024-01-15-email-logs.json
├── monthly/
├── config/
│   └── email-config.json
└── logs/
```

## Integration Steps

1. **Install Dependencies**
   ```bash
   npm install nodemailer uuid
   ```

2. **Set Environment Variables**
   - Add email credentials to `.env` file

3. **Add to Sidebar** (Optional)
   - Import and add FeedbackPage to your routing

4. **Test the System**
   - Use the feedback form to send a test email
   - Check the dashboard for analytics

## Features Included

- ✅ Customer feedback form with validation
- ✅ Direct email sending to any recipient
- ✅ File-based data storage (no database required)
- ✅ Analytics dashboard with metrics
- ✅ Email delivery tracking
- ✅ Export to CSV functionality
- ✅ Multi-theme support (dark, light, midnight)
- ✅ Responsive design
- ✅ Error handling and validation
- ✅ Real-time updates

## Usage

```tsx
import { FeedbackPage } from './components/Feedback';

// Use as a complete page
<FeedbackPage />

// Or use individual components
import { FeedbackForm, FeedbackDashboard } from './components/Feedback';
```

## Next Steps (Phase 2)

- Enhanced email tracking (read receipts, click tracking)
- Advanced analytics and charts
- Email template customization
- Automated responses
- Integration with external email services