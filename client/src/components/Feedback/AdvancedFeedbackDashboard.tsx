import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import InteractiveCharts from './InteractiveCharts';
import EmailTrackingDashboard from './EmailTrackingDashboard';
import EmailTemplateEditor from './EmailTemplateEditor';
import advancedAnalyticsService, { AdvancedMetrics } from './advancedAnalyticsService';

type DashboardView = 'overview' | 'analytics' | 'email-tracking' | 'templates' | 'settings';

interface AdvancedMetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const AdvancedMetricCard: React.FC<AdvancedMetricCardProps> = ({ 
  title, 
  value, 
  subtitle, 
  icon, 
  color, 
  trend 
}) => {
  return (
    <div className="p-6 rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105" style={{
      background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
      border: '1px solid var(--color-border)'
    }}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
            {title}
          </p>
          <p className="text-3xl font-bold mt-2" style={{ color: 'var(--color-text)' }}>
            {value}
          </p>
          {subtitle && (
            <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
              {subtitle}
            </p>
          )}
          {trend && (
            <div className={`flex items-center mt-2 text-xs ${
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              <svg className={`w-3 h-3 mr-1 ${trend.isPositive ? '' : 'rotate-180'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
              </svg>
              <span>{Math.abs(trend.value)}% vs last period</span>
            </div>
          )}
        </div>
        <div className="p-4 rounded-xl" style={{ backgroundColor: `${color}20` }}>
          <div style={{ color }}>{icon}</div>
        </div>
      </div>
    </div>
  );
};

const AdvancedFeedbackDashboard: React.FC = () => {
  const { currentTheme } = useTheme();
  const [currentView, setCurrentView] = useState<DashboardView>('overview');
  const [timeRange, setTimeRange] = useState<'1d' | '7d' | '30d' | '90d'>('30d');
  const [advancedMetrics, setAdvancedMetrics] = useState<AdvancedMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAdvancedMetrics();
  }, [timeRange]);

  const loadAdvancedMetrics = async () => {
    try {
      setLoading(true);
      setError(null);
      const analyticsData = await advancedAnalyticsService.getAdvancedAnalytics(timeRange);
      setAdvancedMetrics(analyticsData.metrics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load advanced metrics');
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    {
      id: 'overview' as DashboardView,
      name: 'Overview',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      id: 'analytics' as DashboardView,
      name: 'Analytics',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 'email-tracking' as DashboardView,
      name: 'Email Tracking',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 'templates' as DashboardView,
      name: 'Templates',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
        </svg>
      )
    },
    {
      id: 'settings' as DashboardView,
      name: 'Settings',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    }
  ];

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Advanced Metrics Grid */}
      {advancedMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <AdvancedMetricCard
            title="Total Feedback"
            value={formatNumber(advancedMetrics.totalFeedbacks)}
            subtitle={`${timeRange} period`}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            }
            color="var(--color-primary)"
            trend={{ value: 12.5, isPositive: true }}
          />
          
          <AdvancedMetricCard
            title="Delivery Rate"
            value={`${advancedMetrics.deliveryRate.toFixed(1)}%`}
            subtitle="Email success rate"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            }
            color="var(--color-success)"
            trend={{ value: 3.2, isPositive: true }}
          />
          
          <AdvancedMetricCard
            title="Avg Response Time"
            value={formatTime(advancedMetrics.averageResponseTime)}
            subtitle="Time to process"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
            color="var(--color-info)"
            trend={{ value: 8.1, isPositive: false }}
          />
          
          <AdvancedMetricCard
            title="Sentiment Score"
            value={`${advancedMetrics.sentimentScore.toFixed(1)}`}
            subtitle="Overall satisfaction"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
            color="var(--color-warning)"
            trend={{ value: 5.7, isPositive: true }}
          />
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Breakdown */}
        {advancedMetrics && (
          <div className="p-6 rounded-xl shadow-lg" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
              Feedback Categories
            </h3>
            <div className="space-y-3">
              {Object.entries(advancedMetrics.categoryBreakdown)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([category, count]) => {
                  const total = Object.values(advancedMetrics.categoryBreakdown).reduce((sum, val) => sum + val, 0);
                  const percentage = total > 0 ? (count / total) * 100 : 0;
                  
                  return (
                    <div key={category} className="flex items-center justify-between">
                      <span className="text-sm font-medium" style={{ color: 'var(--color-text)' }}>
                        {category}
                      </span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 h-2 rounded-full" style={{ backgroundColor: 'var(--color-border)' }}>
                          <div 
                            className="h-2 rounded-full transition-all duration-300"
                            style={{ 
                              backgroundColor: 'var(--color-primary)',
                              width: `${percentage}%`
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8 text-right" style={{ color: 'var(--color-text-muted)' }}>
                          {count}
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Peak Hours */}
        {advancedMetrics && (
          <div className="p-6 rounded-xl shadow-lg" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
              Peak Activity Hours
            </h3>
            <div className="space-y-3">
              {advancedMetrics.peakHours.slice(0, 5).map((hourData, index) => (
                <div key={hourData.hour} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white"
                         style={{ backgroundColor: `hsl(${(index * 60) % 360}, 70%, 50%)` }}>
                      {index + 1}
                    </div>
                    <span className="text-sm font-medium" style={{ color: 'var(--color-text)' }}>
                      {hourData.hour}:00 - {hourData.hour + 1}:00
                    </span>
                  </div>
                  <span className="text-sm font-bold" style={{ color: 'var(--color-text)' }}>
                    {hourData.count} submissions
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Recent Activity */}
      <div className="p-6 rounded-xl shadow-lg" style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}>
        <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
          System Status
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center"
                 style={{ backgroundColor: 'var(--color-success)20' }}>
              <svg className="w-6 h-6" style={{ color: 'var(--color-success)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="font-semibold" style={{ color: 'var(--color-text)' }}>System Online</p>
            <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>All services operational</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center"
                 style={{ backgroundColor: 'var(--color-info)20' }}>
              <svg className="w-6 h-6" style={{ color: 'var(--color-info)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="font-semibold" style={{ color: 'var(--color-text)' }}>Email Service</p>
            <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Ready to send</p>
          </div>
          
          <div className="text-center">
            <div className="w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center"
                 style={{ backgroundColor: 'var(--color-primary)20' }}>
              <svg className="w-6 h-6" style={{ color: 'var(--color-primary)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="font-semibold" style={{ color: 'var(--color-text)' }}>Analytics</p>
            <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Data processing</p>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading && !advancedMetrics) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <svg className="animate-spin w-12 h-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p className="text-lg" style={{ color: 'var(--color-text-muted)' }}>Loading advanced dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-bg)' }}>
      {/* Navigation Header */}
      <div className="sticky top-0 z-10 border-b" style={{
        backgroundColor: 'var(--color-surface)',
        borderColor: 'var(--color-border)'
      }}>
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                Advanced Feedback Dashboard
              </h1>
              <p className="text-sm mt-1" style={{ color: 'var(--color-text-muted)' }}>
                Comprehensive analytics and email tracking
              </p>
            </div>

            {/* Time Range Selector */}
            <div className="flex items-center space-x-4">
              <div className="flex rounded-lg overflow-hidden border" style={{ borderColor: 'var(--color-border)' }}>
                {(['1d', '7d', '30d', '90d'] as const).map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-4 py-2 text-sm font-medium transition-colors ${
                      timeRange === range ? 'text-white' : ''
                    }`}
                    style={{
                      backgroundColor: timeRange === range 
                        ? 'var(--color-primary)' 
                        : 'var(--color-surface)',
                      color: timeRange === range 
                        ? 'white' 
                        : 'var(--color-text)'
                    }}
                  >
                    {range === '1d' ? 'Today' : range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : '90 Days'}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex space-x-1 mt-6 overflow-x-auto">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentView(item.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                  currentView === item.id ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: currentView === item.id 
                    ? 'var(--color-primary)' 
                    : 'transparent',
                  color: currentView === item.id 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                {item.icon}
                <span>{item.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {currentView === 'overview' && renderOverview()}
        {currentView === 'analytics' && <InteractiveCharts timeRange={timeRange} />}
        {currentView === 'email-tracking' && <EmailTrackingDashboard timeRange={timeRange} />}
        {currentView === 'templates' && <EmailTemplateEditor />}
        {currentView === 'settings' && (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-text)' }}>
              Settings Panel
            </h2>
            <p style={{ color: 'var(--color-text-muted)' }}>
              Advanced settings and configuration options coming soon...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedFeedbackDashboard;