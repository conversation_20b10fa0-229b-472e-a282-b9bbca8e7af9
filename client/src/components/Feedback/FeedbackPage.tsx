import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import FeedbackForm from './FeedbackForm';
import FeedbackDashboard from './FeedbackDashboard';
import AdvancedFeedbackDashboard from './AdvancedFeedbackDashboard';
import UltimateFeedbackDashboard from './UltimateFeedbackDashboard';

type FeedbackPageView = 'form' | 'dashboard' | 'advanced' | 'ultimate';

const FeedbackPage: React.FC = () => {
  const { currentTheme } = useTheme();
  const [currentView, setCurrentView] = useState<FeedbackPageView>('ultimate');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [lastSubmissionId, setLastSubmissionId] = useState<string>('');

  const handleSubmitSuccess = (feedbackId: string) => {
    setLastSubmissionId(feedbackId);
    setShowSuccessMessage(true);
    // Auto-hide success message after 5 seconds
    setTimeout(() => setShowSuccessMessage(false), 5000);
  };

  const handleSubmitError = (error: string) => {
    console.error('Feedback submission error:', error);
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-bg)' }}>
      {/* Navigation Header */}
      <div 
        className="sticky top-0 z-10 border-b"
        style={{
          backgroundColor: 'var(--color-surface)',
          borderColor: 'var(--color-border)'
        }}
      >
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                Feedback System
              </h1>
              <p className="text-sm mt-1" style={{ color: 'var(--color-text-muted)' }}>
                {currentView === 'dashboard' 
                  ? 'Monitor and track feedback submissions' 
                  : 'Submit feedback directly via email'
                }
              </p>
            </div>

            {/* View Toggle */}
            <div className="flex rounded-lg overflow-hidden border" style={{ borderColor: 'var(--color-border)' }}>
              <button
                onClick={() => setCurrentView('ultimate')}
                className={`px-3 py-2 text-sm font-medium transition-colors flex items-center space-x-2 ${
                  currentView === 'ultimate' ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: currentView === 'ultimate' 
                    ? 'var(--color-primary)' 
                    : 'var(--color-surface)',
                  color: currentView === 'ultimate' 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <span>AI Ultimate</span>
              </button>
              <button
                onClick={() => setCurrentView('advanced')}
                className={`px-3 py-2 text-sm font-medium transition-colors flex items-center space-x-2 ${
                  currentView === 'advanced' ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: currentView === 'advanced' 
                    ? 'var(--color-primary)' 
                    : 'var(--color-surface)',
                  color: currentView === 'advanced' 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>Advanced</span>
              </button>
              <button
                onClick={() => setCurrentView('dashboard')}
                className={`px-3 py-2 text-sm font-medium transition-colors flex items-center space-x-2 ${
                  currentView === 'dashboard' ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: currentView === 'dashboard' 
                    ? 'var(--color-primary)' 
                    : 'var(--color-surface)',
                  color: currentView === 'dashboard' 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>Basic</span>
              </button>
              <button
                onClick={() => setCurrentView('form')}
                className={`px-3 py-2 text-sm font-medium transition-colors flex items-center space-x-2 ${
                  currentView === 'form' ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: currentView === 'form' 
                    ? 'var(--color-primary)' 
                    : 'var(--color-surface)',
                  color: currentView === 'form' 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                <span>Send Feedback</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {showSuccessMessage && (
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div 
            className="p-4 rounded-lg border flex items-center justify-between"
            style={{
              backgroundColor: 'var(--color-success)20',
              borderColor: 'var(--color-success)',
              color: 'var(--color-success)'
            }}
          >
            <div className="flex items-center space-x-3">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <div>
                <p className="font-medium">Feedback submitted successfully!</p>
                <p className="text-sm opacity-90">
                  Feedback ID: {lastSubmissionId} - Email has been sent to the recipient.
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowSuccessMessage(false)}
              className="p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1">
        {currentView === 'ultimate' ? (
          <UltimateFeedbackDashboard />
        ) : currentView === 'advanced' ? (
          <AdvancedFeedbackDashboard />
        ) : currentView === 'dashboard' ? (
          <FeedbackDashboard />
        ) : (
          <div className="py-8">
            <FeedbackForm 
              onSubmitSuccess={handleSubmitSuccess}
              onSubmitError={handleSubmitError}
              isEmbedded={true}
            />
          </div>
        )}
      </div>

      {/* Footer */}
      <div 
        className="border-t mt-12"
        style={{
          backgroundColor: 'var(--color-surface)',
          borderColor: 'var(--color-border)'
        }}
      >
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* About */}
            <div>
              <h3 className="font-semibold mb-3" style={{ color: 'var(--color-text)' }}>
                About Feedback System
              </h3>
              <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                A comprehensive feedback management system that allows customers to send feedback 
                directly to specified email addresses with full tracking and analytics.
              </p>
            </div>

            {/* Features */}
            <div>
              <h3 className="font-semibold mb-3" style={{ color: 'var(--color-text)' }}>
                Key Features
              </h3>
              <ul className="text-sm space-y-1" style={{ color: 'var(--color-text-muted)' }}>
                <li>• Direct email delivery</li>
                <li>• Real-time tracking</li>
                <li>• Analytics dashboard</li>
                <li>• Export capabilities</li>
                <li>• Multi-theme support</li>
              </ul>
            </div>

            {/* Stats */}
            <div>
              <h3 className="font-semibold mb-3" style={{ color: 'var(--color-text)' }}>
                Quick Stats
              </h3>
              <div className="text-sm space-y-1" style={{ color: 'var(--color-text-muted)' }}>
                <p>Current Theme: <span className="capitalize">{currentTheme}</span></p>
                <p>View: {currentView === 'dashboard' ? 'Dashboard' : 'Feedback Form'}</p>
                <p>Status: System Active</p>
              </div>
            </div>
          </div>

          <div className="border-t mt-8 pt-6 text-center" style={{ borderColor: 'var(--color-border)' }}>
            <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
              © 2024 Feedback System - Part of PinnacleAi Application
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackPage;