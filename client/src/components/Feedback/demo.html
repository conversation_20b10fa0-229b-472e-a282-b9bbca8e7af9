<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feedback System Demo</title>
    <style>
        :root {
            --color-bg: #f8fafc;
            --color-surface: #ffffff;
            --color-surface-light: #f1f5f9;
            --color-border: #e2e8f0;
            --color-text: #1e293b;
            --color-text-muted: #64748b;
            --color-primary: #3b82f6;
            --color-secondary: #8b5cf6;
            --color-success: #10b981;
            --color-error: #ef4444;
            --color-warning: #f59e0b;
            --color-info: #06b6d4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            background-color: var(--color-bg);
            color: var(--color-text);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--color-text);
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            color: var(--color-text-muted);
        }

        .demo-section {
            background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%);
            border: 2px solid var(--color-border);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .demo-section h2 {
            color: var(--color-text);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .demo-section p {
            color: var(--color-text-muted);
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: var(--color-text);
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--color-border);
            border-radius: 0.5rem;
            background: var(--color-surface);
            color: var(--color-text);
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--color-primary);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .btn {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status-message {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            display: none;
        }

        .status-message.success {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--color-success);
            color: var(--color-success);
        }

        .status-message.error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--color-error);
            color: var(--color-error);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature {
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
        }

        .feature-icon {
            width: 3rem;
            height: 3rem;
            background: var(--color-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature h3 {
            color: var(--color-text);
            margin-bottom: 0.5rem;
        }

        .feature p {
            color: var(--color-text-muted);
            font-size: 0.9rem;
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Feedback System Demo</h1>
            <p>Test the feedback system functionality</p>
        </div>

        <!-- Demo Form -->
        <div class="demo-section">
            <h2>📝 Send Test Feedback</h2>
            <p>Use this form to test the feedback submission functionality:</p>
            
            <div id="statusMessage" class="status-message"></div>
            
            <form id="feedbackForm">
                <div class="form-group">
                    <label for="customerName">Your Name *</label>
                    <input type="text" id="customerName" name="customerName" required 
                           placeholder="Enter your full name" value="Demo User">
                </div>
                
                <div class="form-group">
                    <label for="customerEmail">Your Email (Optional)</label>
                    <input type="email" id="customerEmail" name="customerEmail" 
                           placeholder="<EMAIL>" value="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="recipientEmail">Send Feedback To *</label>
                    <input type="email" id="recipientEmail" name="recipientEmail" required 
                           placeholder="<EMAIL>" value="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="feedbackContent">Your Feedback *</label>
                    <textarea id="feedbackContent" name="feedbackContent" required 
                              placeholder="Please share your feedback, suggestions, or comments here...">This is a demo feedback submission to test the system functionality. The feedback system is working great!</textarea>
                </div>
                
                <button type="submit" class="btn" id="submitBtn">
                    <span id="submitText">Send Feedback</span>
                </button>
            </form>
        </div>

        <!-- Features -->
        <div class="demo-section">
            <h2>✨ System Features</h2>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📧</div>
                    <h3>Direct Email</h3>
                    <p>Send feedback directly to any email address</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h3>Analytics</h3>
                    <p>Track submissions and delivery rates</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">💾</div>
                    <h3>File Storage</h3>
                    <p>No database required, uses JSON files</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎨</div>
                    <h3>Multi-Theme</h3>
                    <p>Supports dark, light, and midnight themes</p>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="demo-section">
            <h2>🧪 Test Results</h2>
            <p>Backend test results:</p>
            <div id="testResults" style="background: #f8f9fa; padding: 1rem; border-radius: 0.5rem; font-family: monospace; font-size: 0.9rem; margin-top: 1rem;">
                ✅ Backend service initialized<br>
                ✅ File storage system working<br>
                ✅ Email service configured (demo mode)<br>
                ✅ Data directory structure created<br>
                📁 Data stored in: /home/<USER>/PinnacleAi/DATA/feedback/
            </div>
        </div>
    </div>

    <script>
        // Demo JavaScript functionality
        document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const statusMessage = document.getElementById('statusMessage');
            
            // Show loading state
            submitBtn.disabled = true;
            submitText.innerHTML = '<span class="loading"></span>Sending...';
            
            // Get form data
            const formData = {
                customerName: document.getElementById('customerName').value,
                customerEmail: document.getElementById('customerEmail').value,
                recipientEmail: document.getElementById('recipientEmail').value,
                feedbackContent: document.getElementById('feedbackContent').value
            };
            
            try {
                // Simulate API call (in real implementation, this would call your backend)
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Show success message
                statusMessage.className = 'status-message success';
                statusMessage.style.display = 'block';
                statusMessage.innerHTML = `
                    <strong>✅ Success!</strong> Feedback submitted successfully!<br>
                    <small>Feedback ID: fb_${Date.now()}_demo</small><br>
                    <small>In a real implementation, this would send an email to: ${formData.recipientEmail}</small>
                `;
                
                // Reset form
                document.getElementById('feedbackForm').reset();
                
            } catch (error) {
                // Show error message
                statusMessage.className = 'status-message error';
                statusMessage.style.display = 'block';
                statusMessage.innerHTML = `
                    <strong>❌ Error!</strong> Failed to submit feedback.<br>
                    <small>${error.message}</small>
                `;
            } finally {
                // Reset button
                submitBtn.disabled = false;
                submitText.innerHTML = 'Send Feedback';
            }
        });
        
        // Auto-hide status messages after 10 seconds
        setInterval(() => {
            const statusMessage = document.getElementById('statusMessage');
            if (statusMessage.style.display === 'block') {
                setTimeout(() => {
                    statusMessage.style.display = 'none';
                }, 10000);
            }
        }, 1000);
    </script>
</body>
</html>