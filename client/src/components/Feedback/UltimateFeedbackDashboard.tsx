import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import RealTimeDashboard from './RealTimeDashboard';
import AIResponseGenerator from './AIResponseGenerator';
import AdvancedReporting from './AdvancedReporting';
import AdvancedFeedbackDashboard from './AdvancedFeedbackDashboard';
import aiAnalyticsService, { AIInsight, RealTimeAlert } from './aiAnalyticsService';

type UltimateDashboardView = 'overview' | 'realtime' | 'ai-responses' | 'reporting' | 'advanced' | 'settings';

interface SystemStatus {
  overall: 'excellent' | 'good' | 'warning' | 'critical';
  components: {
    database: 'online' | 'offline' | 'degraded';
    emailService: 'online' | 'offline' | 'degraded';
    aiEngine: 'online' | 'offline' | 'degraded';
    analytics: 'online' | 'offline' | 'degraded';
  };
  uptime: string;
  lastCheck: string;
}

const UltimateFeedbackDashboard: React.FC = () => {
  const { currentTheme } = useTheme();
  const [currentView, setCurrentView] = useState<UltimateDashboardView>('overview');
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    overall: 'excellent',
    components: {
      database: 'online',
      emailService: 'online',
      aiEngine: 'online',
      analytics: 'online'
    },
    uptime: '99.9%',
    lastCheck: new Date().toISOString()
  });
  
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [alerts, setAlerts] = useState<RealTimeAlert[]>([]);
  const [quickStats, setQuickStats] = useState({
    totalFeedback: 0,
    pendingResponses: 0,
    aiAccuracy: 0,
    systemHealth: 100
  });

  useEffect(() => {
    loadDashboardData();
    
    // Set up real-time updates
    const interval = setInterval(() => {
      updateSystemStatus();
      loadQuickStats();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      const [newInsights, newAlerts] = await Promise.all([
        aiAnalyticsService.generateInsights('7d'),
        aiAnalyticsService.checkForAlerts()
      ]);
      
      setInsights(newInsights);
      setAlerts(newAlerts);
      loadQuickStats();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const loadQuickStats = () => {
    // Simulate loading quick stats
    setQuickStats({
      totalFeedback: 1247 + Math.floor(Math.random() * 10),
      pendingResponses: 23 + Math.floor(Math.random() * 5),
      aiAccuracy: 94.2 + Math.random() * 2,
      systemHealth: 98 + Math.random() * 2
    });
  };

  const updateSystemStatus = () => {
    // Simulate system status updates
    const statuses = ['online', 'online', 'online', 'degraded'] as const;
    const randomStatus = () => statuses[Math.floor(Math.random() * statuses.length)];
    
    setSystemStatus(prev => ({
      ...prev,
      components: {
        database: Math.random() > 0.95 ? randomStatus() : 'online',
        emailService: Math.random() > 0.98 ? randomStatus() : 'online',
        aiEngine: Math.random() > 0.97 ? randomStatus() : 'online',
        analytics: Math.random() > 0.99 ? randomStatus() : 'online'
      },
      lastCheck: new Date().toISOString()
    }));
  };

  const navigationItems = [
    {
      id: 'overview' as UltimateDashboardView,
      name: 'AI Overview',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      badge: insights.filter(i => i.impact === 'high' || i.impact === 'critical').length
    },
    {
      id: 'realtime' as UltimateDashboardView,
      name: 'Real-time',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      badge: alerts.filter(a => !a.acknowledged).length
    },
    {
      id: 'ai-responses' as UltimateDashboardView,
      name: 'AI Responses',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
        </svg>
      ),
      badge: quickStats.pendingResponses
    },
    {
      id: 'reporting' as UltimateDashboardView,
      name: 'Reports',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'advanced' as UltimateDashboardView,
      name: 'Advanced',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 'settings' as UltimateDashboardView,
      name: 'Settings',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      online: 'var(--color-success)',
      degraded: 'var(--color-warning)',
      offline: 'var(--color-error)',
      excellent: 'var(--color-success)',
      good: 'var(--color-info)',
      warning: 'var(--color-warning)',
      critical: 'var(--color-error)'
    };
    return colors[status as keyof typeof colors] || 'var(--color-text-muted)';
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Hero Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="p-6 rounded-xl shadow-lg relative overflow-hidden" style={{
          background: `linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark, var(--color-primary)) 100%)`,
          color: 'white'
        }}>
          <div className="relative z-10">
            <h3 className="text-sm font-medium opacity-90">Total Feedback</h3>
            <p className="text-3xl font-bold mt-2">{quickStats.totalFeedback.toLocaleString()}</p>
            <p className="text-sm opacity-75 mt-1">+12% this month</p>
          </div>
          <div className="absolute top-4 right-4 opacity-20">
            <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        </div>

        <div className="p-6 rounded-xl shadow-lg relative overflow-hidden" style={{
          background: `linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark, var(--color-secondary)) 100%)`,
          color: 'white'
        }}>
          <div className="relative z-10">
            <h3 className="text-sm font-medium opacity-90">AI Accuracy</h3>
            <p className="text-3xl font-bold mt-2">{quickStats.aiAccuracy.toFixed(1)}%</p>
            <p className="text-sm opacity-75 mt-1">+2.3% improvement</p>
          </div>
          <div className="absolute top-4 right-4 opacity-20">
            <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
        </div>

        <div className="p-6 rounded-xl shadow-lg relative overflow-hidden" style={{
          background: `linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark, var(--color-warning)) 100%)`,
          color: 'white'
        }}>
          <div className="relative z-10">
            <h3 className="text-sm font-medium opacity-90">Pending Responses</h3>
            <p className="text-3xl font-bold mt-2">{quickStats.pendingResponses}</p>
            <p className="text-sm opacity-75 mt-1">Avg: 2.1 hours</p>
          </div>
          <div className="absolute top-4 right-4 opacity-20">
            <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>

        <div className="p-6 rounded-xl shadow-lg relative overflow-hidden" style={{
          background: `linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark, var(--color-success)) 100%)`,
          color: 'white'
        }}>
          <div className="relative z-10">
            <h3 className="text-sm font-medium opacity-90">System Health</h3>
            <p className="text-3xl font-bold mt-2">{quickStats.systemHealth.toFixed(1)}%</p>
            <p className="text-sm opacity-75 mt-1">All systems operational</p>
          </div>
          <div className="absolute top-4 right-4 opacity-20">
            <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="p-6 rounded-xl shadow-lg" style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
            System Status
          </h3>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full animate-pulse" style={{
              backgroundColor: getStatusColor(systemStatus.overall)
            }} />
            <span className="text-sm font-medium capitalize" style={{
              color: getStatusColor(systemStatus.overall)
            }}>
              {systemStatus.overall}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(systemStatus.components).map(([component, status]) => (
            <div key={component} className="p-3 rounded-lg border" style={{
              backgroundColor: 'var(--color-background)',
              borderColor: getStatusColor(status)
            }}>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium capitalize" style={{ color: 'var(--color-text)' }}>
                  {component.replace(/([A-Z])/g, ' $1').trim()}
                </span>
                <div className="w-2 h-2 rounded-full" style={{
                  backgroundColor: getStatusColor(status)
                }} />
              </div>
              <p className="text-xs mt-1 capitalize" style={{
                color: getStatusColor(status)
              }}>
                {status}
              </p>
            </div>
          ))}
        </div>
        
        <div className="mt-4 flex items-center justify-between text-sm" style={{ color: 'var(--color-text-muted)' }}>
          <span>Uptime: {systemStatus.uptime}</span>
          <span>Last check: {new Date(systemStatus.lastCheck).toLocaleTimeString()}</span>
        </div>
      </div>

      {/* AI Insights & Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Critical Insights */}
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
            Critical AI Insights
          </h3>
          
          <div className="space-y-3">
            {insights.filter(i => i.impact === 'critical' || i.impact === 'high').slice(0, 3).map((insight) => (
              <div key={insight.id} className="p-3 rounded-lg border-l-4" style={{
                backgroundColor: 'var(--color-background)',
                borderLeftColor: insight.impact === 'critical' ? 'var(--color-error)' : 'var(--color-warning)'
              }}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm" style={{ color: 'var(--color-text)' }}>
                      {insight.title}
                    </h4>
                    <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
                      {insight.description}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ml-2 ${
                    insight.impact === 'critical' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {insight.impact}
                  </span>
                </div>
                <div className="mt-2 flex items-center justify-between">
                  <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                    Confidence: {Math.round(insight.confidence * 100)}%
                  </span>
                  {insight.actionable && (
                    <button className="text-xs px-2 py-1 rounded" style={{
                      backgroundColor: 'var(--color-primary)20',
                      color: 'var(--color-primary)'
                    }}>
                      View Actions
                    </button>
                  )}
                </div>
              </div>
            ))}
            
            {insights.filter(i => i.impact === 'critical' || i.impact === 'high').length === 0 && (
              <div className="text-center py-6">
                <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm" style={{ color: 'var(--color-success)' }}>
                  No critical issues detected
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Active Alerts */}
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
            Active Alerts
          </h3>
          
          <div className="space-y-3">
            {alerts.filter(a => !a.acknowledged).slice(0, 3).map((alert) => (
              <div key={alert.id} className="p-3 rounded-lg border" style={{
                backgroundColor: 'var(--color-background)',
                borderColor: getStatusColor(alert.severity)
              }}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm" style={{ color: 'var(--color-text)' }}>
                      {alert.title}
                    </h4>
                    <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
                      {alert.message}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ml-2 ${
                    alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                    alert.severity === 'error' ? 'bg-red-100 text-red-800' :
                    alert.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {alert.severity}
                  </span>
                </div>
                <div className="mt-2 flex items-center justify-between">
                  <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </span>
                  <button
                    onClick={() => aiAnalyticsService.acknowledgeAlert(alert.id)}
                    className="text-xs px-2 py-1 rounded"
                    style={{
                      backgroundColor: 'var(--color-success)20',
                      color: 'var(--color-success)'
                    }}
                  >
                    Acknowledge
                  </button>
                </div>
              </div>
            ))}
            
            {alerts.filter(a => !a.acknowledged).length === 0 && (
              <div className="text-center py-6">
                <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm" style={{ color: 'var(--color-success)' }}>
                  No active alerts
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 rounded-xl shadow-lg" style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}>
        <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
          Quick Actions
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: 'Generate Report', icon: '📊', action: () => setCurrentView('reporting') },
            { label: 'AI Responses', icon: '🤖', action: () => setCurrentView('ai-responses') },
            { label: 'Real-time View', icon: '⚡', action: () => setCurrentView('realtime') },
            { label: 'System Settings', icon: '⚙️', action: () => setCurrentView('settings') }
          ].map((item, index) => (
            <button
              key={index}
              onClick={item.action}
              className="p-4 rounded-lg border text-center transition-all hover:shadow-md"
              style={{
                backgroundColor: 'var(--color-background)',
                borderColor: 'var(--color-border)'
              }}
            >
              <div className="text-2xl mb-2">{item.icon}</div>
              <p className="text-sm font-medium" style={{ color: 'var(--color-text)' }}>
                {item.label}
              </p>
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-6">
      <div className="text-center py-12">
        <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-text)' }}>
          Advanced Settings
        </h2>
        <p style={{ color: 'var(--color-text-muted)' }}>
          AI configuration, system preferences, and enterprise settings would be available here in a full implementation.
        </p>
        
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          {[
            { title: 'AI Configuration', desc: 'Tune AI models and accuracy thresholds' },
            { title: 'Email Settings', desc: 'Configure SMTP, templates, and delivery' },
            { title: 'User Management', desc: 'Manage team access and permissions' },
            { title: 'API Configuration', desc: 'Set up webhooks and integrations' },
            { title: 'Data Retention', desc: 'Configure backup and archival policies' },
            { title: 'Security Settings', desc: 'Manage encryption and compliance' }
          ].map((setting, index) => (
            <div key={index} className="p-4 rounded-lg border text-left" style={{
              backgroundColor: 'var(--color-surface)',
              borderColor: 'var(--color-border)'
            }}>
              <h3 className="font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                {setting.title}
              </h3>
              <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                {setting.desc}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-bg)' }}>
      {/* Enhanced Navigation Header */}
      <div className="sticky top-0 z-20 border-b backdrop-blur-sm" style={{
        backgroundColor: 'var(--color-surface)95',
        borderColor: 'var(--color-border)'
      }}>
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl flex items-center justify-center" style={{
                  background: `linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%)`
                }}>
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-xl font-bold" style={{ color: 'var(--color-text)' }}>
                    Ultimate AI Feedback System
                  </h1>
                  <p className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                    Phase 3 - Enterprise AI Platform
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* System Health Indicator */}
              <div className="flex items-center space-x-2 px-3 py-1 rounded-full" style={{
                backgroundColor: `${getStatusColor(systemStatus.overall)}20`
              }}>
                <div className="w-2 h-2 rounded-full animate-pulse" style={{
                  backgroundColor: getStatusColor(systemStatus.overall)
                }} />
                <span className="text-xs font-medium" style={{
                  color: getStatusColor(systemStatus.overall)
                }}>
                  System {systemStatus.overall}
                </span>
              </div>
              
              {/* Notification Bell */}
              <div className="relative">
                <button className="p-2 rounded-lg transition-colors" style={{
                  backgroundColor: 'var(--color-surface)',
                  color: 'var(--color-text)'
                }}>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>
                </button>
                {alerts.filter(a => !a.acknowledged).length > 0 && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white" style={{
                    backgroundColor: 'var(--color-error)'
                  }}>
                    {alerts.filter(a => !a.acknowledged).length}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Navigation Tabs */}
          <div className="flex space-x-1 mt-6 overflow-x-auto">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentView(item.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all whitespace-nowrap relative ${
                  currentView === item.id ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: currentView === item.id 
                    ? 'var(--color-primary)' 
                    : 'transparent',
                  color: currentView === item.id 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                {item.icon}
                <span>{item.name}</span>
                {item.badge && item.badge > 0 && (
                  <div className="w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold" style={{
                    backgroundColor: currentView === item.id ? 'rgba(255,255,255,0.2)' : 'var(--color-error)',
                    color: currentView === item.id ? 'white' : 'white'
                  }}>
                    {item.badge}
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {currentView === 'overview' && renderOverview()}
        {currentView === 'realtime' && <RealTimeDashboard />}
        {currentView === 'ai-responses' && <AIResponseGenerator />}
        {currentView === 'reporting' && <AdvancedReporting />}
        {currentView === 'advanced' && <AdvancedFeedbackDashboard />}
        {currentView === 'settings' && renderSettings()}
      </div>
    </div>
  );
};

export default UltimateFeedbackDashboard;