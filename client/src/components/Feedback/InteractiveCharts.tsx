import React, { useState, useEffect, useMemo } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import advancedAnalyticsService, { TimeSeriesData, SentimentAnalysis, GeographicData } from './advancedAnalyticsService';

interface ChartProps {
  data: any;
  title: string;
  height?: number;
}

// Simple Line Chart Component
const LineChart: React.FC<{
  data: TimeSeriesData[];
  title: string;
  height?: number;
}> = ({ data, title, height = 300 }) => {
  const { currentTheme } = useTheme();
  
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <p style={{ color: 'var(--color-text-muted)' }}>No data available</p>
      </div>
    );
  }

  const maxSubmissions = Math.max(...data.map(d => d.submissions));
  const maxDeliveryRate = 100;

  return (
    <div className="relative" style={{ height }}>
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
        {title}
      </h3>
      
      <div className="relative w-full h-full">
        <svg width="100%" height="100%" viewBox="0 0 800 250" className="overflow-visible">
          {/* Grid lines */}
          {[0, 1, 2, 3, 4].map(i => (
            <line
              key={i}
              x1="50"
              y1={50 + (i * 40)}
              x2="750"
              y2={50 + (i * 40)}
              stroke="var(--color-border)"
              strokeWidth="1"
              opacity="0.3"
            />
          ))}
          
          {/* Y-axis labels */}
          {[0, 1, 2, 3, 4].map(i => (
            <text
              key={i}
              x="40"
              y={55 + (i * 40)}
              fill="var(--color-text-muted)"
              fontSize="12"
              textAnchor="end"
            >
              {Math.round(maxSubmissions - (i * maxSubmissions / 4))}
            </text>
          ))}
          
          {/* Submissions line */}
          <polyline
            fill="none"
            stroke="var(--color-primary)"
            strokeWidth="3"
            points={data.map((d, i) => {
              const x = 50 + (i * (700 / (data.length - 1)));
              const y = 210 - ((d.submissions / maxSubmissions) * 160);
              return `${x},${y}`;
            }).join(' ')}
          />
          
          {/* Delivery rate line */}
          <polyline
            fill="none"
            stroke="var(--color-success)"
            strokeWidth="2"
            strokeDasharray="5,5"
            points={data.map((d, i) => {
              const x = 50 + (i * (700 / (data.length - 1)));
              const y = 210 - ((d.deliveryRate / maxDeliveryRate) * 160);
              return `${x},${y}`;
            }).join(' ')}
          />
          
          {/* Data points */}
          {data.map((d, i) => {
            const x = 50 + (i * (700 / (data.length - 1)));
            const y = 210 - ((d.submissions / maxSubmissions) * 160);
            return (
              <circle
                key={i}
                cx={x}
                cy={y}
                r="4"
                fill="var(--color-primary)"
                className="hover:r-6 transition-all cursor-pointer"
              >
                <title>{`${d.date}: ${d.submissions} submissions, ${d.deliveryRate}% delivery rate`}</title>
              </circle>
            );
          })}
          
          {/* X-axis labels */}
          {data.map((d, i) => {
            if (i % Math.ceil(data.length / 6) === 0) {
              const x = 50 + (i * (700 / (data.length - 1)));
              return (
                <text
                  key={i}
                  x={x}
                  y="235"
                  fill="var(--color-text-muted)"
                  fontSize="10"
                  textAnchor="middle"
                >
                  {new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </text>
              );
            }
            return null;
          })}
        </svg>
        
        {/* Legend */}
        <div className="absolute top-4 right-4 flex space-x-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: 'var(--color-primary)' }}></div>
            <span style={{ color: 'var(--color-text-muted)' }}>Submissions</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-1 border-dashed border-2" style={{ borderColor: 'var(--color-success)' }}></div>
            <span style={{ color: 'var(--color-text-muted)' }}>Delivery Rate</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Pie Chart Component for Sentiment Analysis
const PieChart: React.FC<{
  data: SentimentAnalysis;
  title: string;
  height?: number;
}> = ({ data, title, height = 300 }) => {
  const total = data.positive + data.negative + data.neutral;
  
  if (total === 0) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <p style={{ color: 'var(--color-text-muted)' }}>No sentiment data available</p>
      </div>
    );
  }

  const positiveAngle = (data.positive / total) * 360;
  const negativeAngle = (data.negative / total) * 360;
  const neutralAngle = (data.neutral / total) * 360;

  const createArcPath = (startAngle: number, endAngle: number, radius: number = 80) => {
    const start = polarToCartesian(100, 100, radius, endAngle);
    const end = polarToCartesian(100, 100, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    return `M 100 100 L ${start.x} ${start.y} A ${radius} ${radius} 0 ${largeArcFlag} 0 ${end.x} ${end.y} Z`;
  };

  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  return (
    <div className="relative" style={{ height }}>
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
        {title}
      </h3>
      
      <div className="flex items-center justify-center">
        <svg width="200" height="200" viewBox="0 0 200 200">
          {/* Positive segment */}
          <path
            d={createArcPath(0, positiveAngle)}
            fill="var(--color-success)"
            className="hover:opacity-80 cursor-pointer"
          >
            <title>{`Positive: ${data.positive} (${Math.round((data.positive / total) * 100)}%)`}</title>
          </path>
          
          {/* Negative segment */}
          <path
            d={createArcPath(positiveAngle, positiveAngle + negativeAngle)}
            fill="var(--color-error)"
            className="hover:opacity-80 cursor-pointer"
          >
            <title>{`Negative: ${data.negative} (${Math.round((data.negative / total) * 100)}%)`}</title>
          </path>
          
          {/* Neutral segment */}
          <path
            d={createArcPath(positiveAngle + negativeAngle, 360)}
            fill="var(--color-text-muted)"
            className="hover:opacity-80 cursor-pointer"
          >
            <title>{`Neutral: ${data.neutral} (${Math.round((data.neutral / total) * 100)}%)`}</title>
          </path>
          
          {/* Center circle */}
          <circle cx="100" cy="100" r="30" fill="var(--color-surface)" />
          <text x="100" y="105" textAnchor="middle" fill="var(--color-text)" fontSize="14" fontWeight="bold">
            {total}
          </text>
        </svg>
        
        {/* Legend */}
        <div className="ml-8 space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--color-success)' }}></div>
            <span className="text-sm" style={{ color: 'var(--color-text)' }}>
              Positive ({data.positive})
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--color-error)' }}></div>
            <span className="text-sm" style={{ color: 'var(--color-text)' }}>
              Negative ({data.negative})
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--color-text-muted)' }}></div>
            <span className="text-sm" style={{ color: 'var(--color-text)' }}>
              Neutral ({data.neutral})
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Bar Chart Component
const BarChart: React.FC<{
  data: { [key: string]: number };
  title: string;
  height?: number;
  color?: string;
}> = ({ data, title, height = 300, color = 'var(--color-primary)' }) => {
  const entries = Object.entries(data).sort(([,a], [,b]) => b - a);
  const maxValue = Math.max(...entries.map(([,value]) => value));
  
  if (entries.length === 0) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <p style={{ color: 'var(--color-text-muted)' }}>No data available</p>
      </div>
    );
  }

  return (
    <div className="relative" style={{ height }}>
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
        {title}
      </h3>
      
      <div className="space-y-3">
        {entries.map(([key, value]) => {
          const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
          return (
            <div key={key} className="flex items-center space-x-3">
              <div className="w-24 text-sm text-right" style={{ color: 'var(--color-text)' }}>
                {key}
              </div>
              <div className="flex-1 relative">
                <div 
                  className="h-6 rounded transition-all duration-300 hover:opacity-80"
                  style={{ 
                    backgroundColor: color,
                    width: `${percentage}%`,
                    minWidth: '2px'
                  }}
                />
                <span 
                  className="absolute right-2 top-0 h-6 flex items-center text-xs font-medium"
                  style={{ color: percentage > 50 ? 'white' : 'var(--color-text)' }}
                >
                  {value}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Heatmap Component for Peak Hours
const HeatmapChart: React.FC<{
  data: { hour: number; count: number }[];
  title: string;
  height?: number;
}> = ({ data, title, height = 200 }) => {
  const maxCount = Math.max(...data.map(d => d.count));
  const hours = Array.from({ length: 24 }, (_, i) => i);
  
  const getIntensity = (hour: number) => {
    const hourData = data.find(d => d.hour === hour);
    return hourData ? (hourData.count / maxCount) : 0;
  };

  return (
    <div className="relative" style={{ height }}>
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
        {title}
      </h3>
      
      <div className="grid grid-cols-12 gap-1">
        {hours.map(hour => {
          const intensity = getIntensity(hour);
          const count = data.find(d => d.hour === hour)?.count || 0;
          
          return (
            <div
              key={hour}
              className="aspect-square rounded flex items-center justify-center text-xs font-medium cursor-pointer hover:scale-110 transition-transform"
              style={{
                backgroundColor: `rgba(59, 130, 246, ${intensity})`,
                color: intensity > 0.5 ? 'white' : 'var(--color-text)',
                border: '1px solid var(--color-border)'
              }}
              title={`${hour}:00 - ${count} submissions`}
            >
              {hour}
            </div>
          );
        })}
      </div>
      
      <div className="mt-2 flex items-center justify-between text-xs" style={{ color: 'var(--color-text-muted)' }}>
        <span>Less activity</span>
        <div className="flex space-x-1">
          {[0, 0.25, 0.5, 0.75, 1].map(intensity => (
            <div
              key={intensity}
              className="w-3 h-3 rounded"
              style={{ backgroundColor: `rgba(59, 130, 246, ${intensity})` }}
            />
          ))}
        </div>
        <span>More activity</span>
      </div>
    </div>
  );
};

// Main Interactive Charts Component
const InteractiveCharts: React.FC<{
  timeRange: '1d' | '7d' | '30d' | '90d';
}> = ({ timeRange }) => {
  const { currentTheme } = useTheme();
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await advancedAnalyticsService.getAdvancedAnalytics(timeRange);
      setAnalyticsData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <svg className="animate-spin w-8 h-8 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p style={{ color: 'var(--color-text-muted)' }}>Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="p-6 rounded-lg border" style={{
          backgroundColor: 'var(--color-error)20',
          borderColor: 'var(--color-error)',
          color: 'var(--color-error)'
        }}>
          <h3 className="text-lg font-semibold mb-2">Error Loading Charts</h3>
          <p className="mb-4">{error}</p>
          <button
            onClick={loadAnalyticsData}
            className="px-4 py-2 rounded-lg font-medium"
            style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return null;
  }

  return (
    <div className="space-y-8">
      {/* Time Series Chart */}
      <div className="p-6 rounded-xl shadow-lg" style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}>
        <LineChart
          data={analyticsData.timeSeries}
          title="Feedback Submissions Over Time"
          height={350}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentiment Analysis */}
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <PieChart
            data={analyticsData.sentiment}
            title="Sentiment Analysis"
            height={300}
          />
        </div>

        {/* Category Breakdown */}
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <BarChart
            data={analyticsData.metrics.categoryBreakdown}
            title="Feedback Categories"
            height={300}
            color="var(--color-secondary)"
          />
        </div>

        {/* Email Provider Stats */}
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <BarChart
            data={analyticsData.metrics.emailProviderStats}
            title="Email Provider Distribution"
            height={300}
            color="var(--color-info)"
          />
        </div>

        {/* Peak Hours Heatmap */}
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <HeatmapChart
            data={analyticsData.metrics.peakHours}
            title="Peak Activity Hours"
            height={200}
          />
        </div>
      </div>

      {/* Geographic Distribution */}
      <div className="p-6 rounded-xl shadow-lg" style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}>
        <BarChart
          data={analyticsData.metrics.geographicDistribution}
          title="Geographic Distribution"
          height={300}
          color="var(--color-warning)"
        />
      </div>
    </div>
  );
};

export default InteractiveCharts;