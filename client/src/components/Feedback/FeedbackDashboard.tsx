import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import feedbackService, { DashboardData } from './feedbackService';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, subtitle, icon, color }) => {
  return (
    <div 
      className="p-6 rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl"
      style={{
        background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
        border: '1px solid var(--color-border)'
      }}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
            {title}
          </p>
          <p className="text-2xl font-bold mt-1" style={{ color: 'var(--color-text)' }}>
            {value}
          </p>
          {subtitle && (
            <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
              {subtitle}
            </p>
          )}
        </div>
        <div 
          className="p-3 rounded-lg"
          style={{ backgroundColor: `${color}20` }}
        >
          <div style={{ color }}>{icon}</div>
        </div>
      </div>
    </div>
  );
};

const FeedbackDashboard: React.FC = () => {
  const { currentTheme } = useTheme();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'1d' | '7d' | '30d'>('7d');
  const [refreshing, setRefreshing] = useState(false);

  const loadDashboardData = async (range: '1d' | '7d' | '30d' = timeRange) => {
    try {
      setError(null);
      const data = await feedbackService.getDashboardData(range);
      setDashboardData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const handleTimeRangeChange = (range: '1d' | '7d' | '30d') => {
    setTimeRange(range);
    setLoading(true);
    loadDashboardData(range);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleExportData = async () => {
    try {
      const csvContent = await feedbackService.exportFeedbackData(timeRange);
      const filename = `feedback-data-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`;
      feedbackService.downloadCSV(csvContent, filename);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && !dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <svg className="animate-spin w-8 h-8 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle 
              className="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              strokeWidth="4"
            />
            <path 
              className="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <p style={{ color: 'var(--color-text-muted)' }}>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error && !dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div 
          className="p-6 rounded-lg border text-center max-w-md"
          style={{
            backgroundColor: 'var(--color-error)20',
            borderColor: 'var(--color-error)',
            color: 'var(--color-error)'
          }}
        >
          <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => {
              setLoading(true);
              setError(null);
              loadDashboardData();
            }}
            className="px-4 py-2 rounded-lg font-medium"
            style={{
              backgroundColor: 'var(--color-primary)',
              color: 'white'
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold" style={{ color: 'var(--color-text)' }}>
            Feedback Dashboard
          </h1>
          <p className="mt-2" style={{ color: 'var(--color-text-muted)' }}>
            Monitor and track customer feedback submissions
          </p>
        </div>
        
        <div className="flex items-center space-x-4 mt-4 sm:mt-0">
          {/* Time Range Selector */}
          <div className="flex rounded-lg overflow-hidden border" style={{ borderColor: 'var(--color-border)' }}>
            {(['1d', '7d', '30d'] as const).map((range) => (
              <button
                key={range}
                onClick={() => handleTimeRangeChange(range)}
                className={`px-4 py-2 text-sm font-medium transition-colors ${
                  timeRange === range ? 'text-white' : ''
                }`}
                style={{
                  backgroundColor: timeRange === range 
                    ? 'var(--color-primary)' 
                    : 'var(--color-surface)',
                  color: timeRange === range 
                    ? 'white' 
                    : 'var(--color-text)'
                }}
              >
                {range === '1d' ? 'Today' : range === '7d' ? '7 Days' : '30 Days'}
              </button>
            ))}
          </div>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-2 rounded-lg transition-colors"
            style={{
              backgroundColor: 'var(--color-surface)',
              color: 'var(--color-text)',
              border: '1px solid var(--color-border)'
            }}
          >
            <svg 
              className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>

          {/* Export Button */}
          <button
            onClick={handleExportData}
            className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            style={{
              backgroundColor: 'var(--color-secondary)',
              color: 'white'
            }}
          >
            Export CSV
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Feedbacks"
            value={dashboardData.metrics.totalFeedbacks}
            subtitle={`Last ${timeRange}`}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            }
            color="var(--color-primary)"
          />
          
          <MetricCard
            title="Emails Sent"
            value={dashboardData.metrics.successfulEmails}
            subtitle="Successfully delivered"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            }
            color="var(--color-success)"
          />
          
          <MetricCard
            title="Failed Emails"
            value={dashboardData.metrics.failedEmails}
            subtitle="Delivery failed"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
            color="var(--color-error)"
          />
          
          <MetricCard
            title="Delivery Rate"
            value={`${dashboardData.metrics.deliveryRate}%`}
            subtitle="Success rate"
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            color="var(--color-info)"
          />
        </div>
      )}

      {/* Recent Submissions */}
      {dashboardData && (
        <div 
          className="rounded-xl shadow-lg p-6"
          style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold" style={{ color: 'var(--color-text)' }}>
              Recent Feedback Submissions
            </h2>
            <span 
              className="text-sm px-3 py-1 rounded-full"
              style={{
                backgroundColor: 'var(--color-primary)20',
                color: 'var(--color-primary)'
              }}
            >
              {dashboardData.recentSubmissions.length} submissions
            </span>
          </div>

          {dashboardData.recentSubmissions.length === 0 ? (
            <div className="text-center py-12">
              <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <p className="text-lg font-medium mb-2" style={{ color: 'var(--color-text)' }}>
                No feedback submissions yet
              </p>
              <p style={{ color: 'var(--color-text-muted)' }}>
                Feedback submissions will appear here once customers start sending feedback
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {dashboardData.recentSubmissions.map((submission) => (
                <div 
                  key={submission.id}
                  className="p-4 rounded-lg border transition-all duration-200 hover:shadow-md"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)'
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium" style={{ color: 'var(--color-text)' }}>
                          {submission.customerName}
                        </h3>
                        <span 
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            submission.emailSent 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}
                          style={{
                            backgroundColor: submission.emailSent 
                              ? 'var(--color-success)20' 
                              : 'var(--color-error)20',
                            color: submission.emailSent 
                              ? 'var(--color-success)' 
                              : 'var(--color-error)'
                          }}
                        >
                          {submission.emailSent ? 'Sent' : 'Failed'}
                        </span>
                      </div>
                      <p className="text-sm mb-2" style={{ color: 'var(--color-text-muted)' }}>
                        To: {submission.recipientEmail}
                      </p>
                      <p className="text-sm" style={{ color: 'var(--color-text)' }}>
                        {submission.preview}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {formatDate(submission.timestamp)}
                      </p>
                      <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
                        ID: {submission.id.substring(0, 12)}...
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FeedbackDashboard;