import React, { useState, useEffect, useCallback } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import aiAnalyticsService, { AIInsight, RealTimeAlert, SmartSentimentAnalysis } from './aiAnalyticsService';
import advancedAnalyticsService from './advancedAnalyticsService';

interface RealTimeMetrics {
  liveSubmissions: number;
  activeSessions: number;
  responseTime: number;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
  lastUpdate: string;
}

interface LiveFeedItem {
  id: string;
  type: 'submission' | 'email_sent' | 'email_opened' | 'alert' | 'insight';
  timestamp: string;
  title: string;
  description: string;
  severity?: 'info' | 'warning' | 'error' | 'success';
  data?: any;
}

const RealTimeDashboard: React.FC = () => {
  const { currentTheme } = useTheme();
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics>({
    liveSubmissions: 0,
    activeSessions: 0,
    responseTime: 0,
    systemHealth: 'good',
    lastUpdate: new Date().toISOString()
  });
  
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [alerts, setAlerts] = useState<RealTimeAlert[]>([]);
  const [liveFeed, setLiveFeed] = useState<LiveFeedItem[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Simulate real-time data updates
  const simulateRealTimeData = useCallback(() => {
    // Simulate live metrics
    setRealTimeMetrics(prev => ({
      liveSubmissions: prev.liveSubmissions + Math.floor(Math.random() * 3),
      activeSessions: 15 + Math.floor(Math.random() * 10),
      responseTime: 120 + Math.floor(Math.random() * 100),
      systemHealth: Math.random() > 0.9 ? 'warning' : 'good',
      lastUpdate: new Date().toISOString()
    }));

    // Simulate live feed updates
    const feedTypes = ['submission', 'email_sent', 'email_opened'] as const;
    const randomType = feedTypes[Math.floor(Math.random() * feedTypes.length)];
    
    const newFeedItem: LiveFeedItem = {
      id: `feed_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`,
      type: randomType,
      timestamp: new Date().toISOString(),
      title: '',
      description: '',
      severity: 'info'
    };

    switch (randomType) {
      case 'submission':
        newFeedItem.title = 'New Feedback Received';
        newFeedItem.description = `Feedback from ${['Alice', 'Bob', 'Carol', 'David', 'Eva'][Math.floor(Math.random() * 5)]} Johnson`;
        newFeedItem.severity = 'success';
        break;
      case 'email_sent':
        newFeedItem.title = 'Email Notification Sent';
        newFeedItem.description = 'Feedback notification delivered successfully';
        newFeedItem.severity = 'info';
        break;
      case 'email_opened':
        newFeedItem.title = 'Email Opened';
        newFeedItem.description = 'Recipient opened feedback notification';
        newFeedItem.severity = 'info';
        break;
    }

    setLiveFeed(prev => [newFeedItem, ...prev.slice(0, 49)]); // Keep last 50 items
  }, []);

  // Load AI insights and alerts
  const loadAIData = useCallback(async () => {
    try {
      const [newInsights, newAlerts] = await Promise.all([
        aiAnalyticsService.generateInsights('7d'),
        aiAnalyticsService.checkForAlerts()
      ]);
      
      setInsights(newInsights);
      setAlerts(newAlerts);

      // Add insights and alerts to live feed
      newInsights.forEach(insight => {
        const feedItem: LiveFeedItem = {
          id: `insight_${insight.id}`,
          type: 'insight',
          timestamp: insight.timestamp,
          title: `AI Insight: ${insight.title}`,
          description: insight.description,
          severity: insight.impact === 'critical' ? 'error' : insight.impact === 'high' ? 'warning' : 'info',
          data: insight
        };
        setLiveFeed(prev => [feedItem, ...prev.slice(0, 49)]);
      });

      newAlerts.forEach(alert => {
        const feedItem: LiveFeedItem = {
          id: `alert_${alert.id}`,
          type: 'alert',
          timestamp: alert.timestamp,
          title: `Alert: ${alert.title}`,
          description: alert.message,
          severity: alert.severity === 'critical' ? 'error' : alert.severity,
          data: alert
        };
        setLiveFeed(prev => [feedItem, ...prev.slice(0, 49)]);
      });

    } catch (error) {
      console.error('Error loading AI data:', error);
    }
  }, []);

  // Simulate WebSocket connection
  useEffect(() => {
    setIsConnected(true);
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        simulateRealTimeData();
      }, 3000); // Update every 3 seconds
    }

    // Load AI data initially and then every 30 seconds
    loadAIData();
    const aiInterval = setInterval(loadAIData, 30000);

    return () => {
      if (interval) clearInterval(interval);
      clearInterval(aiInterval);
    };
  }, [autoRefresh, simulateRealTimeData, loadAIData]);

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getHealthColor = (health: string) => {
    const colors = {
      excellent: 'var(--color-success)',
      good: 'var(--color-info)',
      warning: 'var(--color-warning)',
      critical: 'var(--color-error)'
    };
    return colors[health as keyof typeof colors] || 'var(--color-text-muted)';
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      success: 'var(--color-success)',
      info: 'var(--color-info)',
      warning: 'var(--color-warning)',
      error: 'var(--color-error)'
    };
    return colors[severity as keyof typeof colors] || 'var(--color-text-muted)';
  };

  const handleAcknowledgeAlert = (alertId: string) => {
    aiAnalyticsService.acknowledgeAlert(alertId);
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="flex items-center justify-between p-4 rounded-lg border" style={{
        backgroundColor: isConnected ? 'var(--color-success)20' : 'var(--color-error)20',
        borderColor: isConnected ? 'var(--color-success)' : 'var(--color-error)'
      }}>
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'animate-pulse' : ''}`} style={{
            backgroundColor: isConnected ? 'var(--color-success)' : 'var(--color-error)'
          }} />
          <span className="font-medium" style={{ color: 'var(--color-text)' }}>
            {isConnected ? 'Real-time Connected' : 'Connection Lost'}
          </span>
          <span className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
            Last update: {formatTimestamp(realTimeMetrics.lastUpdate)}
          </span>
        </div>
        
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm" style={{ color: 'var(--color-text)' }}>Auto-refresh</span>
          </label>
          
          <button
            onClick={() => {
              simulateRealTimeData();
              loadAIData();
            }}
            className="px-3 py-1 rounded text-sm font-medium"
            style={{
              backgroundColor: 'var(--color-primary)20',
              color: 'var(--color-primary)'
            }}
          >
            Refresh Now
          </button>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="p-4 rounded-lg border" style={{
          backgroundColor: 'var(--color-surface)',
          borderColor: 'var(--color-border)'
        }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                Live Submissions
              </p>
              <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                {realTimeMetrics.liveSubmissions}
              </p>
            </div>
            <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-primary)20' }}>
              <svg className="w-5 h-5" style={{ color: 'var(--color-primary)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>

        <div className="p-4 rounded-lg border" style={{
          backgroundColor: 'var(--color-surface)',
          borderColor: 'var(--color-border)'
        }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                Active Sessions
              </p>
              <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                {realTimeMetrics.activeSessions}
              </p>
            </div>
            <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-info)20' }}>
              <svg className="w-5 h-5" style={{ color: 'var(--color-info)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="p-4 rounded-lg border" style={{
          backgroundColor: 'var(--color-surface)',
          borderColor: 'var(--color-border)'
        }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                Response Time
              </p>
              <p className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                {realTimeMetrics.responseTime}ms
              </p>
            </div>
            <div className="p-2 rounded-lg" style={{ backgroundColor: 'var(--color-warning)20' }}>
              <svg className="w-5 h-5" style={{ color: 'var(--color-warning)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="p-4 rounded-lg border" style={{
          backgroundColor: 'var(--color-surface)',
          borderColor: 'var(--color-border)'
        }}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                System Health
              </p>
              <p className="text-lg font-bold capitalize" style={{ color: getHealthColor(realTimeMetrics.systemHealth) }}>
                {realTimeMetrics.systemHealth}
              </p>
            </div>
            <div className="p-2 rounded-lg" style={{ backgroundColor: `${getHealthColor(realTimeMetrics.systemHealth)}20` }}>
              <svg className="w-5 h-5" style={{ color: getHealthColor(realTimeMetrics.systemHealth) }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* AI Insights */}
        <div className="lg:col-span-1">
          <div className="p-6 rounded-xl shadow-lg h-96 overflow-hidden" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
                AI Insights
              </h3>
              <span className="px-2 py-1 rounded-full text-xs font-medium" style={{
                backgroundColor: 'var(--color-primary)20',
                color: 'var(--color-primary)'
              }}>
                {insights.length} insights
              </span>
            </div>
            
            <div className="space-y-3 overflow-y-auto h-80">
              {insights.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    Generating AI insights...
                  </p>
                </div>
              ) : (
                insights.map((insight) => (
                  <div key={insight.id} className="p-3 rounded-lg border" style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: insight.impact === 'critical' ? 'var(--color-error)' : 
                                insight.impact === 'high' ? 'var(--color-warning)' : 'var(--color-border)'
                  }}>
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm" style={{ color: 'var(--color-text)' }}>
                        {insight.title}
                      </h4>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        insight.impact === 'critical' ? 'bg-red-100 text-red-800' :
                        insight.impact === 'high' ? 'bg-yellow-100 text-yellow-800' :
                        insight.impact === 'medium' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {insight.impact}
                      </span>
                    </div>
                    <p className="text-xs mb-2" style={{ color: 'var(--color-text-muted)' }}>
                      {insight.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        Confidence: {Math.round(insight.confidence * 100)}%
                      </span>
                      <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {formatTimestamp(insight.timestamp)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="lg:col-span-1">
          <div className="p-6 rounded-xl shadow-lg h-96 overflow-hidden" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
                Active Alerts
              </h3>
              <span className="px-2 py-1 rounded-full text-xs font-medium" style={{
                backgroundColor: alerts.filter(a => !a.acknowledged).length > 0 ? 'var(--color-error)20' : 'var(--color-success)20',
                color: alerts.filter(a => !a.acknowledged).length > 0 ? 'var(--color-error)' : 'var(--color-success)'
              }}>
                {alerts.filter(a => !a.acknowledged).length} active
              </span>
            </div>
            
            <div className="space-y-3 overflow-y-auto h-80">
              {alerts.filter(a => !a.acknowledged).length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    No active alerts
                  </p>
                </div>
              ) : (
                alerts.filter(a => !a.acknowledged).map((alert) => (
                  <div key={alert.id} className="p-3 rounded-lg border" style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: getSeverityColor(alert.severity)
                  }}>
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm" style={{ color: 'var(--color-text)' }}>
                        {alert.title}
                      </h4>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                        alert.severity === 'error' ? 'bg-red-100 text-red-800' :
                        alert.severity === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {alert.severity}
                      </span>
                    </div>
                    <p className="text-xs mb-3" style={{ color: 'var(--color-text-muted)' }}>
                      {alert.message}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {formatTimestamp(alert.timestamp)}
                      </span>
                      <button
                        onClick={() => handleAcknowledgeAlert(alert.id)}
                        className="px-2 py-1 rounded text-xs font-medium"
                        style={{
                          backgroundColor: 'var(--color-success)20',
                          color: 'var(--color-success)'
                        }}
                      >
                        Acknowledge
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Live Activity Feed */}
        <div className="lg:col-span-1">
          <div className="p-6 rounded-xl shadow-lg h-96 overflow-hidden" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
                Live Activity
              </h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
                <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>Live</span>
              </div>
            </div>
            
            <div className="space-y-2 overflow-y-auto h-80">
              {liveFeed.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-9 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2" />
                  </svg>
                  <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                    Waiting for activity...
                  </p>
                </div>
              ) : (
                liveFeed.map((item) => (
                  <div key={item.id} className="flex items-start space-x-3 p-2 rounded hover:bg-opacity-50 transition-colors" style={{
                    backgroundColor: 'var(--color-background)'
                  }}>
                    <div className="w-2 h-2 rounded-full mt-2" style={{
                      backgroundColor: getSeverityColor(item.severity || 'info')
                    }} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate" style={{ color: 'var(--color-text)' }}>
                        {item.title}
                      </p>
                      <p className="text-xs truncate" style={{ color: 'var(--color-text-muted)' }}>
                        {item.description}
                      </p>
                      <p className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {formatTimestamp(item.timestamp)}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeDashboard;