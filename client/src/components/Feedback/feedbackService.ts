// Frontend service for feedback system
export interface FeedbackSubmission {
  customerName: string;
  customerEmail?: string;
  recipientEmail: string;
  feedbackContent: string;
}

export interface FeedbackSubmissionResponse {
  success: boolean;
  message: string;
  feedbackId?: string;
  emailSent?: boolean;
  error?: string;
}

export interface DashboardMetrics {
  totalFeedbacks: number;
  successfulEmails: number;
  failedEmails: number;
  deliveryRate: number;
}

export interface DailyStats {
  [date: string]: {
    total: number;
    sent: number;
    failed: number;
  };
}

export interface RecentSubmission {
  id: string;
  customerName: string;
  customerEmail?: string;
  recipientEmail: string;
  timestamp: string;
  emailSent: boolean;
  preview: string;
  feedbackContent: string;
}

export interface DashboardData {
  metrics: DashboardMetrics;
  dailyStats: DailyStats;
  recentSubmissions: RecentSubmission[];
  timeRange: string;
}

export interface EmailConfig {
  service: string;
  user: string;
  pass: string;
}

class FeedbackService {
  private apiBaseUrl: string = '/api/feedback';

  constructor() {
    // Frontend-only service - uses API calls instead of direct backend service
  }

  /**
   * Submit customer feedback
   */
  async submitFeedback(feedback: FeedbackSubmission): Promise<FeedbackSubmissionResponse> {
    try {
      // Validation
      if (!feedback.customerName || !feedback.recipientEmail || !feedback.feedbackContent) {
        throw new Error('Missing required fields: customerName, recipientEmail, feedbackContent');
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(feedback.recipientEmail)) {
        throw new Error('Invalid recipient email address');
      }

      if (feedback.customerEmail && !emailRegex.test(feedback.customerEmail)) {
        throw new Error('Invalid customer email address');
      }

      // Try to use the backend API first, fallback to frontend simulation
      try {
        const response = await fetch(`${this.apiBaseUrl}/submit`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(feedback)
        });

        if (response.ok) {
          const result = await response.json();
          
          // Also store in localStorage for dashboard display
          if (result.success) {
            this.storeSubmissionLocally(feedback, result);
          }
          
          return result;
        } else {
          // If API fails, fall back to frontend simulation
          console.warn('API submission failed, falling back to frontend simulation');
          return await this.submitFeedbackFrontend(feedback);
        }
      } catch (error) {
        console.warn('API not available, using frontend simulation:', error.message);
        return await this.submitFeedbackFrontend(feedback);
      }

    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  /**
   * Submit feedback frontend implementation
   */
  private async submitFeedbackFrontend(feedback: FeedbackSubmission): Promise<FeedbackSubmissionResponse> {
    // Generate unique ID
    const feedbackId = `fb_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    
    // Save to localStorage for demo purposes
    const submissions = this.getStoredSubmissions();
    const newSubmission = {
      id: feedbackId,
      timestamp: new Date().toISOString(),
      customerName: feedback.customerName,
      customerEmail: feedback.customerEmail || '<EMAIL>',
      recipientEmail: feedback.recipientEmail,
      feedbackContent: feedback.feedbackContent,
      emailStatus: {
        sent: true, // Simulate successful email
        sentAt: new Date().toISOString(),
        delivered: true,
        deliveredAt: new Date().toISOString(),
        failed: false,
        failureReason: null,
        messageId: `msg_${feedbackId}`
      },
      metadata: {
        source: 'feedback_form',
        userAgent: navigator.userAgent
      }
    };

    submissions.push(newSubmission);
    localStorage.setItem('feedback_submissions', JSON.stringify(submissions));

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      message: 'Feedback submitted successfully',
      feedbackId,
      emailSent: true
    };
  }

  /**
   * Get stored submissions from localStorage
   */
  private getStoredSubmissions(): any[] {
    try {
      const stored = localStorage.getItem('feedback_submissions');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error reading stored submissions:', error);
      return [];
    }
  }

  /**
   * Store submission locally for dashboard display
   */
  private storeSubmissionLocally(feedback: FeedbackSubmission, result: any): void {
    try {
      const submissions = this.getStoredSubmissions();
      const newSubmission = {
        id: result.feedbackId || `fb_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`,
        timestamp: new Date().toISOString(),
        customerName: feedback.customerName,
        customerEmail: feedback.customerEmail || '<EMAIL>',
        recipientEmail: feedback.recipientEmail,
        feedbackContent: feedback.feedbackContent,
        emailStatus: {
          sent: result.emailSent || false,
          sentAt: new Date().toISOString(),
          delivered: result.emailSent || false,
          deliveredAt: result.emailSent ? new Date().toISOString() : null,
          failed: !result.emailSent,
          failureReason: result.emailSent ? null : 'Email sending failed',
          messageId: result.messageId || `msg_${result.feedbackId}`
        },
        metadata: {
          source: 'feedback_form',
          userAgent: navigator.userAgent,
          apiResponse: result
        }
      };

      submissions.push(newSubmission);
      localStorage.setItem('feedback_submissions', JSON.stringify(submissions));
    } catch (error) {
      console.error('Error storing submission locally:', error);
    }
  }

  /**
   * Get dashboard data
   */
  async getDashboardData(timeRange: '1d' | '7d' | '30d' = '7d'): Promise<DashboardData> {
    try {
      // Use localStorage data for frontend implementation
      return this.getLocalDashboardData(timeRange);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get dashboard data from localStorage
   */
  private getLocalDashboardData(timeRange: '1d' | '7d' | '30d'): DashboardData {
    const submissions = this.getStoredSubmissions();
    
    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case '1d':
        startDate.setDate(now.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
    }

    // Filter submissions by date range
    const filteredSubmissions = submissions.filter(submission => 
      new Date(submission.timestamp) >= startDate
    );

    // Calculate metrics
    const totalFeedbacks = filteredSubmissions.length;
    const successfulEmails = filteredSubmissions.filter(f => f.emailStatus.sent).length;
    const failedEmails = filteredSubmissions.filter(f => f.emailStatus.failed).length;
    const deliveryRate = totalFeedbacks > 0 ? (successfulEmails / totalFeedbacks * 100) : 0;

    // Group by date for charts
    const dailyStats: DailyStats = {};
    filteredSubmissions.forEach(submission => {
      const date = submission.timestamp.split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { total: 0, sent: 0, failed: 0 };
      }
      dailyStats[date].total++;
      if (submission.emailStatus.sent) dailyStats[date].sent++;
      if (submission.emailStatus.failed) dailyStats[date].failed++;
    });

    // Recent submissions (last 10)
    const recentSubmissions: RecentSubmission[] = filteredSubmissions
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10)
      .map(submission => ({
        id: submission.id,
        customerName: submission.customerName,
        customerEmail: submission.customerEmail,
        recipientEmail: submission.recipientEmail,
        timestamp: submission.timestamp,
        emailSent: submission.emailStatus.sent,
        preview: submission.feedbackContent.substring(0, 100) + '...',
        feedbackContent: submission.feedbackContent
      }));

    return {
      metrics: {
        totalFeedbacks,
        successfulEmails,
        failedEmails,
        deliveryRate: Math.round(deliveryRate * 10) / 10
      },
      dailyStats,
      recentSubmissions,
      timeRange
    };
  }

  /**
   * Update email configuration
   */
  async updateEmailConfig(config: EmailConfig): Promise<{ success: boolean; message: string }> {
    try {
      // Store in localStorage for frontend implementation
      localStorage.setItem('feedback_email_config', JSON.stringify(config));
      
      return {
        success: true,
        message: 'Email configuration updated successfully (stored locally)'
      };

    } catch (error) {
      console.error('Error updating email configuration:', error);
      throw error;
    }
  }

  /**
   * Get email configuration
   */
  async getEmailConfig(): Promise<{ service: string; configured: boolean }> {
    try {
      // Check localStorage
      const stored = localStorage.getItem('feedback_email_config');
      if (stored) {
        const config = JSON.parse(stored);
        return {
          service: config.service || 'gmail',
          configured: !!(config.user && config.pass)
        };
      }

      return {
        service: 'gmail',
        configured: false
      };

    } catch (error) {
      console.error('Error fetching email configuration:', error);
      return {
        service: 'gmail',
        configured: false
      };
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfig(): Promise<{ success: boolean; message: string }> {
    try {
      const testFeedback: FeedbackSubmission = {
        customerName: 'Test User',
        customerEmail: '<EMAIL>',
        recipientEmail: '<EMAIL>',
        feedbackContent: 'This is a test feedback to verify email configuration.'
      };

      const result = await this.submitFeedback(testFeedback);
      
      return {
        success: result.emailSent || false,
        message: result.emailSent 
          ? 'Email configuration test successful!' 
          : 'Email configuration test failed - email not sent'
      };
    } catch (error) {
      return {
        success: false,
        message: `Email configuration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Export feedback data as CSV
   */
  async exportFeedbackData(timeRange: '1d' | '7d' | '30d' = '30d'): Promise<string> {
    try {
      const dashboardData = await this.getDashboardData(timeRange);
      
      // Convert to CSV format
      const headers = ['ID', 'Customer Name', 'Recipient Email', 'Timestamp', 'Email Sent', 'Preview'];
      const csvRows = [headers.join(',')];
      
      dashboardData.recentSubmissions.forEach(submission => {
        const row = [
          submission.id,
          `"${submission.customerName}"`,
          submission.recipientEmail,
          submission.timestamp,
          submission.emailSent ? 'Yes' : 'No',
          `"${submission.preview.replace(/"/g, '""')}"`
        ];
        csvRows.push(row.join(','));
      });
      
      return csvRows.join('\n');
    } catch (error) {
      console.error('Error exporting feedback data:', error);
      throw error;
    }
  }

  /**
   * Download CSV file
   */
  downloadCSV(csvContent: string, filename: string = 'feedback-data.csv'): void {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  }

  /**
   * Clear all feedback data (for testing)
   */
  clearAllData(): void {
    localStorage.removeItem('feedback_submissions');
    localStorage.removeItem('feedback_email_config');
  }
}

export const feedbackService = new FeedbackService();
export default feedbackService;