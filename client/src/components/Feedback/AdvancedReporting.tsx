import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import aiAnalyticsService, { PredictiveAnalytics } from './aiAnalyticsService';
import advancedAnalyticsService from './advancedAnalyticsService';

interface ReportConfig {
  title: string;
  timeRange: '7d' | '30d' | '90d' | '1y';
  includeCharts: boolean;
  includeInsights: boolean;
  includePredictions: boolean;
  includeRawData: boolean;
  format: 'pdf' | 'csv' | 'json';
}

interface ExecutiveSummary {
  totalFeedback: number;
  sentimentTrend: 'improving' | 'declining' | 'stable';
  topIssues: string[];
  keyMetrics: {
    satisfactionScore: number;
    responseRate: number;
    resolutionTime: string;
  };
  recommendations: string[];
}

const AdvancedReporting: React.FC = () => {
  const { currentTheme } = useTheme();
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    title: 'Feedback Analysis Report',
    timeRange: '30d',
    includeCharts: true,
    includeInsights: true,
    includePredictions: true,
    includeRawData: false,
    format: 'pdf'
  });
  
  const [executiveSummary, setExecutiveSummary] = useState<ExecutiveSummary | null>(null);
  const [predictions, setPredictions] = useState<PredictiveAnalytics | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadReportData();
  }, [reportConfig.timeRange]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      
      // Map 1y to 90d for backend compatibility
      const mappedTimeRange = reportConfig.timeRange === '1y' ? '90d' : reportConfig.timeRange;
      
      // Load analytics data
      const analyticsData = await advancedAnalyticsService.getAdvancedAnalytics(mappedTimeRange);
      const insights = await aiAnalyticsService.generateInsights(mappedTimeRange);
      const predictiveData = await aiAnalyticsService.generatePredictions(mappedTimeRange);
      
      // Generate executive summary
      const summary: ExecutiveSummary = {
        totalFeedback: analyticsData.metrics.totalFeedbacks,
        sentimentTrend: analyticsData.metrics.sentimentScore > 3.5 ? 'improving' : 
                       analyticsData.metrics.sentimentScore < 2.5 ? 'declining' : 'stable',
        topIssues: Object.entries(analyticsData.metrics.categoryBreakdown)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 3)
          .map(([category]) => category),
        keyMetrics: {
          satisfactionScore: analyticsData.metrics.sentimentScore,
          responseRate: analyticsData.metrics.deliveryRate,
          resolutionTime: analyticsData.metrics.averageResponseTime < 3600 ? 
            `${Math.round(analyticsData.metrics.averageResponseTime / 60)} minutes` :
            `${Math.round(analyticsData.metrics.averageResponseTime / 3600)} hours`
        },
        recommendations: insights
          .filter(insight => insight.actionable && insight.suggestedActions)
          .slice(0, 5)
          .map(insight => insight.suggestedActions![0])
      };
      
      setExecutiveSummary(summary);
      setPredictions(predictiveData);
    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async () => {
    try {
      setIsGenerating(true);
      
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would generate and download the actual report
      const reportData = {
        config: reportConfig,
        executiveSummary,
        predictions,
        generatedAt: new Date().toISOString()
      };
      
      // Create downloadable content based on format
      let content: string;
      let filename: string;
      let mimeType: string;
      
      switch (reportConfig.format) {
        case 'json':
          content = JSON.stringify(reportData, null, 2);
          filename = `feedback-report-${reportConfig.timeRange}-${Date.now()}.json`;
          mimeType = 'application/json';
          break;
          
        case 'csv':
          content = generateCSVReport(reportData);
          filename = `feedback-report-${reportConfig.timeRange}-${Date.now()}.csv`;
          mimeType = 'text/csv';
          break;
          
        default: // PDF
          content = generatePDFReport(reportData);
          filename = `feedback-report-${reportConfig.timeRange}-${Date.now()}.html`;
          mimeType = 'text/html';
          break;
      }
      
      // Download the report
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateCSVReport = (data: any): string => {
    const lines = [
      'Feedback Analysis Report',
      `Generated: ${new Date().toLocaleString()}`,
      `Time Range: ${reportConfig.timeRange}`,
      '',
      'Executive Summary',
      `Total Feedback,${data.executiveSummary?.totalFeedback || 0}`,
      `Sentiment Trend,${data.executiveSummary?.sentimentTrend || 'N/A'}`,
      `Satisfaction Score,${data.executiveSummary?.keyMetrics.satisfactionScore || 0}`,
      `Response Rate,${data.executiveSummary?.keyMetrics.responseRate || 0}%`,
      `Resolution Time,${data.executiveSummary?.keyMetrics.resolutionTime || 'N/A'}`,
      '',
      'Top Issues',
      ...(data.executiveSummary?.topIssues || []).map((issue: string, index: number) => `${index + 1},${issue}`),
      '',
      'Recommendations',
      ...(data.executiveSummary?.recommendations || []).map((rec: string, index: number) => `${index + 1},"${rec}"`)
    ];
    
    return lines.join('\n');
  };

  const generatePDFReport = (data: any): string => {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>${reportConfig.title}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .metric { display: inline-block; margin: 10px 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .chart-placeholder { height: 200px; background: #f5f5f5; border: 1px solid #ddd; margin: 20px 0; display: flex; align-items: center; justify-content: center; }
        ul { padding-left: 20px; }
        .prediction { background: #e8f4f8; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${reportConfig.title}</h1>
        <p>Generated on ${new Date().toLocaleString()}</p>
        <p>Time Range: ${reportConfig.timeRange}</p>
    </div>
    
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="metric">
            <h3>Total Feedback</h3>
            <p style="font-size: 24px; margin: 0;">${data.executiveSummary?.totalFeedback || 0}</p>
        </div>
        <div class="metric">
            <h3>Sentiment Trend</h3>
            <p style="font-size: 18px; margin: 0; text-transform: capitalize;">${data.executiveSummary?.sentimentTrend || 'N/A'}</p>
        </div>
        <div class="metric">
            <h3>Satisfaction Score</h3>
            <p style="font-size: 24px; margin: 0;">${data.executiveSummary?.keyMetrics.satisfactionScore?.toFixed(1) || 'N/A'}/5.0</p>
        </div>
        <div class="metric">
            <h3>Response Rate</h3>
            <p style="font-size: 24px; margin: 0;">${data.executiveSummary?.keyMetrics.responseRate?.toFixed(1) || 0}%</p>
        </div>
    </div>
    
    <div class="section">
        <h2>Top Issues</h2>
        <ul>
            ${(data.executiveSummary?.topIssues || []).map((issue: string) => `<li>${issue}</li>`).join('')}
        </ul>
    </div>
    
    ${reportConfig.includePredictions && data.predictions ? `
    <div class="section">
        <h2>Predictive Analytics</h2>
        <div class="prediction">
            <h3>Next Week Prediction</h3>
            <p><strong>Expected Submissions:</strong> ${data.predictions.nextWeekPrediction.expectedSubmissions}</p>
            <p><strong>Trend:</strong> ${data.predictions.nextWeekPrediction.trend}</p>
            <p><strong>Confidence:</strong> ${Math.round(data.predictions.nextWeekPrediction.confidence * 100)}%</p>
        </div>
        
        <div class="prediction">
            <h3>Seasonal Patterns</h3>
            <p><strong>Pattern:</strong> ${data.predictions.seasonalPatterns.pattern}</p>
            <p><strong>Next Peak:</strong> ${data.predictions.seasonalPatterns.nextPeak}</p>
        </div>
        
        ${data.predictions.riskFactors.length > 0 ? `
        <div class="prediction">
            <h3>Risk Factors</h3>
            <ul>
                ${data.predictions.riskFactors.map((risk: any) => `
                    <li><strong>${risk.factor}</strong> (${Math.round(risk.probability * 100)}% probability) - ${risk.impact}</li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
    </div>
    ` : ''}
    
    <div class="section">
        <h2>Key Recommendations</h2>
        <ul>
            ${(data.executiveSummary?.recommendations || []).map((rec: string) => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
    
    ${reportConfig.includeCharts ? `
    <div class="section">
        <h2>Charts and Visualizations</h2>
        <div class="chart-placeholder">
            <p>Feedback Trends Chart (Would be generated in actual implementation)</p>
        </div>
        <div class="chart-placeholder">
            <p>Sentiment Analysis Chart (Would be generated in actual implementation)</p>
        </div>
    </div>
    ` : ''}
    
    <div class="section" style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
        <p>This report was generated automatically by the AI-Powered Feedback System</p>
        <p>For questions or support, please contact your system administrator</p>
    </div>
</body>
</html>
    `;
  };

  const scheduleReport = () => {
    // In a real implementation, this would set up scheduled report generation
    alert('Report scheduling feature would be implemented with backend integration');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <svg className="animate-spin w-8 h-8 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p style={{ color: 'var(--color-text-muted)' }}>Loading report data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
            Advanced Reporting
          </h2>
          <p className="text-sm mt-1" style={{ color: 'var(--color-text-muted)' }}>
            Generate comprehensive reports with AI insights and predictions
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={scheduleReport}
            className="px-4 py-2 rounded-lg text-sm font-medium"
            style={{
              backgroundColor: 'var(--color-info)20',
              color: 'var(--color-info)'
            }}
          >
            Schedule Reports
          </button>
          
          <button
            onClick={generateReport}
            disabled={isGenerating}
            className="px-4 py-2 rounded-lg text-sm font-medium text-white disabled:opacity-50"
            style={{ backgroundColor: 'var(--color-primary)' }}
          >
            {isGenerating ? 'Generating...' : 'Generate Report'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Configuration */}
        <div className="lg:col-span-1">
          <div className="p-6 rounded-xl shadow-lg" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
              Report Configuration
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  Report Title
                </label>
                <input
                  type="text"
                  value={reportConfig.title}
                  onChange={(e) => setReportConfig({ ...reportConfig, title: e.target.value })}
                  className="w-full px-3 py-2 rounded border"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  Time Range
                </label>
                <select
                  value={reportConfig.timeRange}
                  onChange={(e) => setReportConfig({ ...reportConfig, timeRange: e.target.value as any })}
                  className="w-full px-3 py-2 rounded border"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                >
                  <option value="7d">Last 7 Days</option>
                  <option value="30d">Last 30 Days</option>
                  <option value="90d">Last 90 Days</option>
                  <option value="1y">Last Year</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  Format
                </label>
                <select
                  value={reportConfig.format}
                  onChange={(e) => setReportConfig({ ...reportConfig, format: e.target.value as any })}
                  className="w-full px-3 py-2 rounded border"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                >
                  <option value="pdf">PDF Report</option>
                  <option value="csv">CSV Data</option>
                  <option value="json">JSON Data</option>
                </select>
              </div>
              
              <div className="space-y-2">
                <label className="block text-sm font-medium" style={{ color: 'var(--color-text)' }}>
                  Include Sections
                </label>
                
                {[
                  { key: 'includeCharts', label: 'Charts & Visualizations' },
                  { key: 'includeInsights', label: 'AI Insights' },
                  { key: 'includePredictions', label: 'Predictive Analytics' },
                  { key: 'includeRawData', label: 'Raw Data Export' }
                ].map(({ key, label }) => (
                  <label key={key} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={reportConfig[key as keyof ReportConfig] as boolean}
                      onChange={(e) => setReportConfig({ 
                        ...reportConfig, 
                        [key]: e.target.checked 
                      })}
                      className="rounded"
                    />
                    <span className="text-sm" style={{ color: 'var(--color-text)' }}>
                      {label}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Executive Summary */}
        <div className="lg:col-span-2">
          <div className="p-6 rounded-xl shadow-lg" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
              Executive Summary Preview
            </h3>
            
            {executiveSummary ? (
              <div className="space-y-6">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
                    <p className="text-2xl font-bold" style={{ color: 'var(--color-primary)' }}>
                      {executiveSummary.totalFeedback}
                    </p>
                    <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                      Total Feedback
                    </p>
                  </div>
                  
                  <div className="text-center p-3 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
                    <p className="text-2xl font-bold" style={{ 
                      color: executiveSummary.sentimentTrend === 'improving' ? 'var(--color-success)' :
                             executiveSummary.sentimentTrend === 'declining' ? 'var(--color-error)' :
                             'var(--color-warning)'
                    }}>
                      {executiveSummary.keyMetrics.satisfactionScore.toFixed(1)}
                    </p>
                    <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                      Satisfaction
                    </p>
                  </div>
                  
                  <div className="text-center p-3 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
                    <p className="text-2xl font-bold" style={{ color: 'var(--color-info)' }}>
                      {executiveSummary.keyMetrics.responseRate.toFixed(1)}%
                    </p>
                    <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                      Response Rate
                    </p>
                  </div>
                  
                  <div className="text-center p-3 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
                    <p className="text-lg font-bold" style={{ color: 'var(--color-secondary)' }}>
                      {executiveSummary.keyMetrics.resolutionTime}
                    </p>
                    <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
                      Avg Resolution
                    </p>
                  </div>
                </div>
                
                {/* Trend Indicator */}
                <div className="p-4 rounded" style={{
                  backgroundColor: executiveSummary.sentimentTrend === 'improving' ? 'var(--color-success)20' :
                                   executiveSummary.sentimentTrend === 'declining' ? 'var(--color-error)20' :
                                   'var(--color-warning)20',
                  border: `1px solid ${
                    executiveSummary.sentimentTrend === 'improving' ? 'var(--color-success)' :
                    executiveSummary.sentimentTrend === 'declining' ? 'var(--color-error)' :
                    'var(--color-warning)'
                  }`
                }}>
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5" style={{
                      color: executiveSummary.sentimentTrend === 'improving' ? 'var(--color-success)' :
                             executiveSummary.sentimentTrend === 'declining' ? 'var(--color-error)' :
                             'var(--color-warning)'
                    }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                            d={executiveSummary.sentimentTrend === 'improving' ? "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" :
                               executiveSummary.sentimentTrend === 'declining' ? "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" :
                               "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"} />
                    </svg>
                    <span className="font-medium" style={{
                      color: executiveSummary.sentimentTrend === 'improving' ? 'var(--color-success)' :
                             executiveSummary.sentimentTrend === 'declining' ? 'var(--color-error)' :
                             'var(--color-warning)'
                    }}>
                      Sentiment is {executiveSummary.sentimentTrend}
                    </span>
                  </div>
                </div>
                
                {/* Top Issues */}
                <div>
                  <h4 className="font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                    Top Issues
                  </h4>
                  <div className="space-y-2">
                    {executiveSummary.topIssues.map((issue, index) => (
                      <div key={index} className="flex items-center space-x-3 p-2 rounded" style={{
                        backgroundColor: 'var(--color-background)'
                      }}>
                        <span className="w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white" style={{
                          backgroundColor: `hsl(${index * 120}, 70%, 50%)`
                        }}>
                          {index + 1}
                        </span>
                        <span style={{ color: 'var(--color-text)' }}>{issue}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Key Recommendations */}
                <div>
                  <h4 className="font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                    Key Recommendations
                  </h4>
                  <ul className="space-y-1">
                    {executiveSummary.recommendations.slice(0, 3).map((rec, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <svg className="w-4 h-4 mt-0.5 flex-shrink-0" style={{ color: 'var(--color-success)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-sm" style={{ color: 'var(--color-text)' }}>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <p style={{ color: 'var(--color-text-muted)' }}>
                  Loading executive summary...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Predictive Analytics Preview */}
      {reportConfig.includePredictions && predictions && (
        <div className="p-6 rounded-xl shadow-lg" style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '1px solid var(--color-border)'
        }}>
          <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
            Predictive Analytics Preview
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Next Week Prediction */}
            <div className="p-4 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
              <h4 className="font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                Next Week Forecast
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Expected:</span>
                  <span className="font-medium" style={{ color: 'var(--color-text)' }}>
                    {predictions.nextWeekPrediction.expectedSubmissions} submissions
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Trend:</span>
                  <span className={`font-medium capitalize ${
                    predictions.nextWeekPrediction.trend === 'increasing' ? 'text-green-600' :
                    predictions.nextWeekPrediction.trend === 'decreasing' ? 'text-red-600' :
                    'text-yellow-600'
                  }`}>
                    {predictions.nextWeekPrediction.trend}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Confidence:</span>
                  <span className="font-medium" style={{ color: 'var(--color-text)' }}>
                    {Math.round(predictions.nextWeekPrediction.confidence * 100)}%
                  </span>
                </div>
              </div>
            </div>
            
            {/* Seasonal Patterns */}
            <div className="p-4 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
              <h4 className="font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                Seasonal Patterns
              </h4>
              <div className="space-y-2">
                <div>
                  <span className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Pattern:</span>
                  <p className="font-medium" style={{ color: 'var(--color-text)' }}>
                    {predictions.seasonalPatterns.pattern}
                  </p>
                </div>
                <div>
                  <span className="text-sm" style={{ color: 'var(--color-text-muted)' }}>Next Peak:</span>
                  <p className="font-medium" style={{ color: 'var(--color-text)' }}>
                    {predictions.seasonalPatterns.nextPeak}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Risk Factors */}
            <div className="p-4 rounded" style={{ backgroundColor: 'var(--color-background)' }}>
              <h4 className="font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                Risk Factors
              </h4>
              <div className="space-y-2">
                {predictions.riskFactors.length === 0 ? (
                  <p className="text-sm" style={{ color: 'var(--color-success)' }}>
                    No significant risks detected
                  </p>
                ) : (
                  predictions.riskFactors.slice(0, 2).map((risk, index) => (
                    <div key={index} className="p-2 rounded" style={{
                      backgroundColor: risk.probability > 0.7 ? 'var(--color-error)20' : 'var(--color-warning)20'
                    }}>
                      <p className="text-sm font-medium" style={{ color: 'var(--color-text)' }}>
                        {risk.factor}
                      </p>
                      <p className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {Math.round(risk.probability * 100)}% probability
                      </p>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedReporting;