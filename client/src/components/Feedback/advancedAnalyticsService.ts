// Advanced Analytics Service for Feedback System Phase 2
import feedbackService, { DashboardData, RecentSubmission } from './feedbackService';

export interface AdvancedMetrics {
  totalFeedbacks: number;
  successfulEmails: number;
  failedEmails: number;
  deliveryRate: number;
  averageResponseTime: number;
  peakHours: { hour: number; count: number }[];
  sentimentScore: number;
  categoryBreakdown: { [category: string]: number };
  geographicDistribution: { [region: string]: number };
  emailProviderStats: { [provider: string]: number };
}

export interface TimeSeriesData {
  date: string;
  submissions: number;
  successful: number;
  failed: number;
  deliveryRate: number;
}

export interface SentimentAnalysis {
  positive: number;
  neutral: number;
  negative: number;
  keywords: { word: string; count: number; sentiment: 'positive' | 'negative' | 'neutral' }[];
}

export interface EmailTrackingData {
  messageId: string;
  feedbackId: string;
  sent: boolean;
  delivered: boolean;
  opened: boolean;
  clicked: boolean;
  bounced: boolean;
  timestamps: {
    sent?: string;
    delivered?: string;
    opened?: string;
    clicked?: string;
    bounced?: string;
  };
  recipientInfo: {
    email: string;
    provider: string;
    location?: string;
  };
}

export interface GeographicData {
  country: string;
  region: string;
  city: string;
  coordinates: [number, number];
  count: number;
  successRate: number;
}

class AdvancedAnalyticsService {
  private sentimentKeywords = {
    positive: [
      'excellent', 'amazing', 'great', 'fantastic', 'wonderful', 'awesome', 'perfect',
      'love', 'best', 'outstanding', 'brilliant', 'superb', 'magnificent', 'incredible',
      'pleased', 'satisfied', 'happy', 'delighted', 'impressed', 'recommend', 'good'
    ],
    negative: [
      'terrible', 'awful', 'horrible', 'bad', 'worst', 'hate', 'disappointed',
      'frustrated', 'angry', 'annoyed', 'poor', 'useless', 'broken', 'failed',
      'slow', 'confusing', 'difficult', 'problem', 'issue', 'bug', 'error'
    ]
  };

  /**
   * Get advanced analytics data
   */
  async getAdvancedAnalytics(timeRange: '1d' | '7d' | '30d' | '90d' = '30d'): Promise<{
    metrics: AdvancedMetrics;
    timeSeries: TimeSeriesData[];
    sentiment: SentimentAnalysis;
    geographic: GeographicData[];
    emailTracking: EmailTrackingData[];
  }> {
    try {
      // Get base dashboard data - map 90d to 30d for now since backend only supports up to 30d
      const mappedTimeRange = timeRange === '90d' ? '30d' : timeRange;
      const dashboardData = await feedbackService.getDashboardData(mappedTimeRange);
      
      // Generate advanced metrics
      const metrics = await this.calculateAdvancedMetrics(dashboardData);
      const timeSeries = this.generateTimeSeriesData(dashboardData);
      const sentiment = this.analyzeSentiment(dashboardData.recentSubmissions);
      const geographic = this.generateGeographicData(dashboardData.recentSubmissions);
      const emailTracking = this.generateEmailTrackingData(dashboardData.recentSubmissions);

      return {
        metrics,
        timeSeries,
        sentiment,
        geographic,
        emailTracking
      };
    } catch (error) {
      console.error('Error getting advanced analytics:', error);
      throw error;
    }
  }

  /**
   * Calculate advanced metrics
   */
  private async calculateAdvancedMetrics(data: DashboardData): Promise<AdvancedMetrics> {
    const submissions = data.recentSubmissions;
    
    // Calculate average response time (simulated)
    const averageResponseTime = this.calculateAverageResponseTime(submissions);
    
    // Calculate peak hours
    const peakHours = this.calculatePeakHours(submissions);
    
    // Calculate sentiment score
    const sentimentScore = this.calculateOverallSentiment(submissions);
    
    // Category breakdown (based on content analysis)
    const categoryBreakdown = this.categorizeSubmissions(submissions);
    
    // Geographic distribution (simulated based on email domains)
    const geographicDistribution = this.analyzeGeographicDistribution(submissions);
    
    // Email provider statistics
    const emailProviderStats = this.analyzeEmailProviders(submissions);

    return {
      totalFeedbacks: data.metrics.totalFeedbacks,
      successfulEmails: data.metrics.successfulEmails,
      failedEmails: data.metrics.failedEmails,
      deliveryRate: data.metrics.deliveryRate,
      averageResponseTime,
      peakHours,
      sentimentScore,
      categoryBreakdown,
      geographicDistribution,
      emailProviderStats
    };
  }

  /**
   * Generate time series data for charts
   */
  private generateTimeSeriesData(data: DashboardData): TimeSeriesData[] {
    const timeSeriesData: TimeSeriesData[] = [];
    
    Object.entries(data.dailyStats).forEach(([date, stats]) => {
      const deliveryRate = stats.total > 0 ? (stats.sent / stats.total) * 100 : 0;
      
      timeSeriesData.push({
        date,
        submissions: stats.total,
        successful: stats.sent,
        failed: stats.failed,
        deliveryRate: Math.round(deliveryRate * 10) / 10
      });
    });
    
    return timeSeriesData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  /**
   * Analyze sentiment of feedback submissions
   */
  private analyzeSentiment(submissions: RecentSubmission[]): SentimentAnalysis {
    let positive = 0;
    let negative = 0;
    let neutral = 0;
    const keywordCounts: { [word: string]: { count: number; sentiment: 'positive' | 'negative' | 'neutral' } } = {};

    submissions.forEach(submission => {
      const content = submission.preview.toLowerCase();
      let sentimentScore = 0;
      
      // Check for positive keywords
      this.sentimentKeywords.positive.forEach(keyword => {
        const matches = (content.match(new RegExp(keyword, 'g')) || []).length;
        if (matches > 0) {
          sentimentScore += matches;
          keywordCounts[keyword] = {
            count: (keywordCounts[keyword]?.count || 0) + matches,
            sentiment: 'positive'
          };
        }
      });
      
      // Check for negative keywords
      this.sentimentKeywords.negative.forEach(keyword => {
        const matches = (content.match(new RegExp(keyword, 'g')) || []).length;
        if (matches > 0) {
          sentimentScore -= matches;
          keywordCounts[keyword] = {
            count: (keywordCounts[keyword]?.count || 0) + matches,
            sentiment: 'negative'
          };
        }
      });
      
      // Classify sentiment
      if (sentimentScore > 0) positive++;
      else if (sentimentScore < 0) negative++;
      else neutral++;
    });

    // Convert to keywords array
    const keywords = Object.entries(keywordCounts)
      .map(([word, data]) => ({ word, count: data.count, sentiment: data.sentiment }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);

    return { positive, negative, neutral, keywords };
  }

  /**
   * Calculate average response time (simulated)
   */
  private calculateAverageResponseTime(submissions: RecentSubmission[]): number {
    // Simulate response times based on submission patterns
    const responseTimes = submissions.map(() => Math.random() * 3600 + 300); // 5 minutes to 1 hour
    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  }

  /**
   * Calculate peak hours for submissions
   */
  private calculatePeakHours(submissions: RecentSubmission[]): { hour: number; count: number }[] {
    const hourCounts: { [hour: number]: number } = {};
    
    submissions.forEach(submission => {
      const hour = new Date(submission.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });
    
    return Object.entries(hourCounts)
      .map(([hour, count]) => ({ hour: parseInt(hour), count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Calculate overall sentiment score
   */
  private calculateOverallSentiment(submissions: RecentSubmission[]): number {
    const sentiment = this.analyzeSentiment(submissions);
    const total = sentiment.positive + sentiment.negative + sentiment.neutral;
    
    if (total === 0) return 0;
    
    return ((sentiment.positive - sentiment.negative) / total) * 100;
  }

  /**
   * Categorize submissions based on content
   */
  private categorizeSubmissions(submissions: RecentSubmission[]): { [category: string]: number } {
    const categories = {
      'Bug Report': ['bug', 'error', 'broken', 'issue', 'problem', 'crash'],
      'Feature Request': ['feature', 'request', 'add', 'new', 'would like', 'suggestion'],
      'Compliment': ['great', 'excellent', 'love', 'amazing', 'perfect', 'good'],
      'Complaint': ['bad', 'terrible', 'awful', 'disappointed', 'frustrated', 'hate'],
      'Question': ['how', 'what', 'why', 'when', 'where', 'question', '?'],
      'General': []
    };
    
    const categoryCounts: { [category: string]: number } = {};
    
    submissions.forEach(submission => {
      const content = submission.preview.toLowerCase();
      let categorized = false;
      
      Object.entries(categories).forEach(([category, keywords]) => {
        if (category === 'General') return;
        
        const hasKeyword = keywords.some(keyword => content.includes(keyword));
        if (hasKeyword && !categorized) {
          categoryCounts[category] = (categoryCounts[category] || 0) + 1;
          categorized = true;
        }
      });
      
      if (!categorized) {
        categoryCounts['General'] = (categoryCounts['General'] || 0) + 1;
      }
    });
    
    return categoryCounts;
  }

  /**
   * Analyze geographic distribution based on email domains
   */
  private analyzeGeographicDistribution(submissions: RecentSubmission[]): { [region: string]: number } {
    const domainToRegion: { [domain: string]: string } = {
      'gmail.com': 'Global',
      'yahoo.com': 'Global',
      'outlook.com': 'Global',
      'hotmail.com': 'Global',
      'company.com': 'North America',
      'example.com': 'Test Region',
      'test.com': 'Test Region'
    };
    
    const regionCounts: { [region: string]: number } = {};
    
    submissions.forEach(submission => {
      const domain = submission.recipientEmail.split('@')[1];
      const region = domainToRegion[domain] || 'Unknown';
      regionCounts[region] = (regionCounts[region] || 0) + 1;
    });
    
    return regionCounts;
  }

  /**
   * Analyze email provider statistics
   */
  private analyzeEmailProviders(submissions: RecentSubmission[]): { [provider: string]: number } {
    const providerCounts: { [provider: string]: number } = {};
    
    submissions.forEach(submission => {
      const domain = submission.recipientEmail.split('@')[1];
      let provider = domain;
      
      // Group common providers
      if (domain.includes('gmail')) provider = 'Gmail';
      else if (domain.includes('yahoo')) provider = 'Yahoo';
      else if (domain.includes('outlook') || domain.includes('hotmail')) provider = 'Microsoft';
      else if (domain.includes('company')) provider = 'Corporate';
      
      providerCounts[provider] = (providerCounts[provider] || 0) + 1;
    });
    
    return providerCounts;
  }

  /**
   * Generate geographic data for mapping
   */
  private generateGeographicData(submissions: RecentSubmission[]): GeographicData[] {
    // Simulated geographic data based on submissions
    const locations = [
      { country: 'United States', region: 'California', city: 'San Francisco', coordinates: [-122.4194, 37.7749] as [number, number] },
      { country: 'United States', region: 'New York', city: 'New York', coordinates: [-74.0060, 40.7128] as [number, number] },
      { country: 'United Kingdom', region: 'England', city: 'London', coordinates: [-0.1276, 51.5074] as [number, number] },
      { country: 'Germany', region: 'Berlin', city: 'Berlin', coordinates: [13.4050, 52.5200] as [number, number] },
      { country: 'Japan', region: 'Tokyo', city: 'Tokyo', coordinates: [139.6917, 35.6895] as [number, number] }
    ];
    
    return locations.map(location => ({
      ...location,
      count: Math.floor(Math.random() * submissions.length) + 1,
      successRate: Math.random() * 40 + 60 // 60-100% success rate
    }));
  }

  /**
   * Generate email tracking data
   */
  private generateEmailTrackingData(submissions: RecentSubmission[]): EmailTrackingData[] {
    return submissions.map(submission => {
      const provider = submission.recipientEmail.split('@')[1];
      const opened = Math.random() > 0.3; // 70% open rate
      const clicked = opened && Math.random() > 0.7; // 30% click rate if opened
      const bounced = Math.random() > 0.95; // 5% bounce rate
      
      return {
        messageId: `msg_${submission.id}`,
        feedbackId: submission.id,
        sent: submission.emailSent,
        delivered: submission.emailSent && !bounced,
        opened,
        clicked,
        bounced,
        timestamps: {
          sent: submission.emailSent ? submission.timestamp : undefined,
          delivered: submission.emailSent && !bounced ? new Date(Date.now() + Math.random() * 3600000).toISOString() : undefined,
          opened: opened ? new Date(Date.now() + Math.random() * 7200000).toISOString() : undefined,
          clicked: clicked ? new Date(Date.now() + Math.random() * 10800000).toISOString() : undefined,
          bounced: bounced ? new Date(Date.now() + Math.random() * 1800000).toISOString() : undefined
        },
        recipientInfo: {
          email: submission.recipientEmail,
          provider,
          location: this.getLocationFromProvider(provider)
        }
      };
    });
  }

  /**
   * Get simulated location from email provider
   */
  private getLocationFromProvider(provider: string): string {
    const providerLocations: { [provider: string]: string } = {
      'gmail.com': 'Global',
      'yahoo.com': 'Global',
      'outlook.com': 'Global',
      'company.com': 'United States',
      'example.com': 'Test Location'
    };
    
    return providerLocations[provider] || 'Unknown';
  }

  /**
   * Export advanced analytics data
   */
  async exportAdvancedAnalytics(timeRange: '1d' | '7d' | '30d' | '90d' = '30d'): Promise<string> {
    try {
      const analytics = await this.getAdvancedAnalytics(timeRange);
      
      // Create comprehensive CSV
      const headers = [
        'Metric', 'Value', 'Category', 'Date', 'Additional Info'
      ];
      
      const rows = [headers.join(',')];
      
      // Add metrics
      Object.entries(analytics.metrics).forEach(([key, value]) => {
        if (typeof value === 'object') {
          Object.entries(value).forEach(([subKey, subValue]) => {
            rows.push([key, subValue, 'Metrics', new Date().toISOString(), subKey].join(','));
          });
        } else {
          rows.push([key, value, 'Metrics', new Date().toISOString(), ''].join(','));
        }
      });
      
      // Add time series data
      analytics.timeSeries.forEach(data => {
        rows.push(['Submissions', data.submissions, 'TimeSeries', data.date, 'Daily Count'].join(','));
        rows.push(['Successful', data.successful, 'TimeSeries', data.date, 'Daily Success'].join(','));
        rows.push(['Failed', data.failed, 'TimeSeries', data.date, 'Daily Failures'].join(','));
      });
      
      return rows.join('\n');
    } catch (error) {
      console.error('Error exporting advanced analytics:', error);
      throw error;
    }
  }
}

export const advancedAnalyticsService = new AdvancedAnalyticsService();
export default advancedAnalyticsService;