import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent: string;
  variables: string[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

const defaultTemplates: EmailTemplate[] = [
  {
    id: 'default',
    name: 'Default Feedback Template',
    subject: 'New Feedback from {{customerName}}',
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">
          New Feedback Received
        </h2>
        
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1e293b; margin-top: 0;">Customer Information</h3>
          <p><strong>Name:</strong> {{customerName}}</p>
          <p><strong>Email:</strong> {{customerEmail}}</p>
          <p><strong>Submitted:</strong> {{timestamp}}</p>
        </div>
        
        <div style="background-color: #ffffff; padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
          <h3 style="color: #1e293b; margin-top: 0;">Feedback Message</h3>
          <p style="line-height: 1.6; color: #475569;">{{feedbackContent}}</p>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background-color: #eff6ff; border-radius: 8px; border-left: 4px solid #3b82f6;">
          <p style="margin: 0; color: #1e40af; font-size: 14px;">
            <strong>Feedback ID:</strong> {{feedbackId}}<br>
            This email was sent automatically by the Feedback System.
          </p>
        </div>
      </div>
    `,
    textContent: `
New Feedback Received

Customer: {{customerName}}
Email: {{customerEmail}}
Submitted: {{timestamp}}

Message:
{{feedbackContent}}

Feedback ID: {{feedbackId}}
    `,
    variables: ['customerName', 'customerEmail', 'timestamp', 'feedbackContent', 'feedbackId'],
    isDefault: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'professional',
    name: 'Professional Template',
    subject: 'Customer Feedback - {{customerName}}',
    htmlContent: `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 650px; margin: 0 auto; background: #ffffff;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 300;">Customer Feedback</h1>
        </div>
        
        <div style="padding: 40px 30px;">
          <div style="background: #f8f9fa; border-left: 4px solid #667eea; padding: 20px; margin-bottom: 30px;">
            <h2 style="color: #2c3e50; margin: 0 0 15px 0; font-size: 20px;">Contact Details</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; color: #7f8c8d; font-weight: 600;">Name:</td>
                <td style="padding: 8px 0; color: #2c3e50;">{{customerName}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #7f8c8d; font-weight: 600;">Email:</td>
                <td style="padding: 8px 0; color: #2c3e50;">{{customerEmail}}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #7f8c8d; font-weight: 600;">Date:</td>
                <td style="padding: 8px 0; color: #2c3e50;">{{timestamp}}</td>
              </tr>
            </table>
          </div>
          
          <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 25px;">
            <h2 style="color: #2c3e50; margin: 0 0 20px 0; font-size: 18px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Message</h2>
            <div style="color: #495057; line-height: 1.8; font-size: 16px;">{{feedbackContent}}</div>
          </div>
          
          <div style="margin-top: 30px; padding: 20px; background: #e8f4f8; border-radius: 8px; text-align: center;">
            <p style="margin: 0; color: #2c3e50; font-size: 14px;">
              <strong>Reference ID:</strong> {{feedbackId}}<br>
              <em>This is an automated message from our feedback system.</em>
            </p>
          </div>
        </div>
      </div>
    `,
    textContent: `
CUSTOMER FEEDBACK
================

Contact Details:
- Name: {{customerName}}
- Email: {{customerEmail}}
- Date: {{timestamp}}

Message:
--------
{{feedbackContent}}

Reference ID: {{feedbackId}}

This is an automated message from our feedback system.
    `,
    variables: ['customerName', 'customerEmail', 'timestamp', 'feedbackContent', 'feedbackId'],
    isDefault: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'minimal',
    name: 'Minimal Template',
    subject: 'Feedback: {{customerName}}',
    htmlContent: `
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 500px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; font-size: 24px; margin-bottom: 20px;">New Feedback</h1>
        
        <p style="color: #666; margin-bottom: 20px;">
          <strong>From:</strong> {{customerName}} ({{customerEmail}})<br>
          <strong>Date:</strong> {{timestamp}}
        </p>
        
        <div style="background: #f5f5f5; padding: 20px; border-radius: 4px; margin-bottom: 20px;">
          <p style="color: #333; line-height: 1.6; margin: 0;">{{feedbackContent}}</p>
        </div>
        
        <p style="color: #999; font-size: 12px; margin: 0;">
          ID: {{feedbackId}}
        </p>
      </div>
    `,
    textContent: `
New Feedback

From: {{customerName}} ({{customerEmail}})
Date: {{timestamp}}

{{feedbackContent}}

ID: {{feedbackId}}
    `,
    variables: ['customerName', 'customerEmail', 'timestamp', 'feedbackContent', 'feedbackId'],
    isDefault: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

const EmailTemplateEditor: React.FC = () => {
  const { currentTheme } = useTheme();
  const [templates, setTemplates] = useState<EmailTemplate[]>(defaultTemplates);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate>(defaultTemplates[0]);
  const [isEditing, setIsEditing] = useState(false);
  const [previewMode, setPreviewMode] = useState<'html' | 'text'>('html');
  const [showPreview, setShowPreview] = useState(false);

  // Sample data for preview
  const sampleData = {
    customerName: 'John Doe',
    customerEmail: '<EMAIL>',
    timestamp: new Date().toLocaleString(),
    feedbackContent: 'This is a sample feedback message to demonstrate how the email template will look when sent to recipients. The system is working great and I love the new features!',
    feedbackId: 'fb_1234567890_sample'
  };

  const handleTemplateChange = (template: EmailTemplate) => {
    if (isEditing) {
      const confirmChange = window.confirm('You have unsaved changes. Are you sure you want to switch templates?');
      if (!confirmChange) return;
    }
    setSelectedTemplate(template);
    setIsEditing(false);
  };

  const handleSaveTemplate = () => {
    const updatedTemplates = templates.map(t => 
      t.id === selectedTemplate.id 
        ? { ...selectedTemplate, updatedAt: new Date().toISOString() }
        : t
    );
    setTemplates(updatedTemplates);
    setIsEditing(false);
    
    // Save to localStorage
    localStorage.setItem('feedback_email_templates', JSON.stringify(updatedTemplates));
  };

  const handleCreateTemplate = () => {
    const newTemplate: EmailTemplate = {
      id: `template_${Date.now()}`,
      name: 'New Template',
      subject: 'New Feedback from {{customerName}}',
      htmlContent: '<p>{{feedbackContent}}</p>',
      textContent: '{{feedbackContent}}',
      variables: ['customerName', 'customerEmail', 'timestamp', 'feedbackContent', 'feedbackId'],
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    setTemplates([...templates, newTemplate]);
    setSelectedTemplate(newTemplate);
    setIsEditing(true);
  };

  const handleDeleteTemplate = (templateId: string) => {
    if (templates.find(t => t.id === templateId)?.isDefault) {
      alert('Cannot delete default template');
      return;
    }
    
    const confirmDelete = window.confirm('Are you sure you want to delete this template?');
    if (!confirmDelete) return;
    
    const updatedTemplates = templates.filter(t => t.id !== templateId);
    setTemplates(updatedTemplates);
    
    if (selectedTemplate.id === templateId) {
      setSelectedTemplate(updatedTemplates[0]);
    }
    
    localStorage.setItem('feedback_email_templates', JSON.stringify(updatedTemplates));
  };

  const renderPreview = () => {
    let content = previewMode === 'html' ? selectedTemplate.htmlContent : selectedTemplate.textContent;
    let subject = selectedTemplate.subject;
    
    // Replace variables with sample data
    Object.entries(sampleData).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      content = content.replace(regex, value);
      subject = subject.replace(regex, value);
    });
    
    return { content, subject };
  };

  useEffect(() => {
    // Load templates from localStorage
    const stored = localStorage.getItem('feedback_email_templates');
    if (stored) {
      try {
        const parsedTemplates = JSON.parse(stored);
        setTemplates(parsedTemplates);
        setSelectedTemplate(parsedTemplates[0]);
      } catch (error) {
        console.error('Error loading templates:', error);
      }
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
            Email Template Editor
          </h2>
          <p className="text-sm mt-1" style={{ color: 'var(--color-text-muted)' }}>
            Customize email templates for feedback notifications
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            style={{
              backgroundColor: showPreview ? 'var(--color-primary)' : 'var(--color-surface)',
              color: showPreview ? 'white' : 'var(--color-text)',
              border: '1px solid var(--color-border)'
            }}
          >
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
          
          <button
            onClick={handleCreateTemplate}
            className="px-4 py-2 rounded-lg text-sm font-medium text-white transition-colors"
            style={{ backgroundColor: 'var(--color-secondary)' }}
          >
            Create Template
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template List */}
        <div className="lg:col-span-1">
          <div className="rounded-xl shadow-lg p-6" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
              Templates
            </h3>
            
            <div className="space-y-2">
              {templates.map((template) => (
                <div
                  key={template.id}
                  className={`p-3 rounded-lg cursor-pointer transition-all ${
                    selectedTemplate.id === template.id ? 'ring-2' : ''
                  }`}
                  style={{
                    backgroundColor: selectedTemplate.id === template.id 
                      ? 'var(--color-primary)20' 
                      : 'var(--color-background)',
                    borderColor: selectedTemplate.id === template.id 
                      ? 'var(--color-primary)' 
                      : 'var(--color-border)',
                    border: '1px solid'
                  }}
                  onClick={() => handleTemplateChange(template)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium" style={{ color: 'var(--color-text)' }}>
                        {template.name}
                      </p>
                      <p className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {template.isDefault ? 'Default' : 'Custom'}
                      </p>
                    </div>
                    
                    {!template.isDefault && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTemplate(template.id);
                        }}
                        className="p-1 rounded text-red-500 hover:bg-red-100 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Template Editor */}
        <div className="lg:col-span-2">
          <div className="rounded-xl shadow-lg p-6" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '1px solid var(--color-border)'
          }}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
                {isEditing ? 'Edit Template' : selectedTemplate.name}
              </h3>
              
              <div className="flex space-x-2">
                {isEditing ? (
                  <>
                    <button
                      onClick={() => setIsEditing(false)}
                      className="px-3 py-1 rounded text-sm"
                      style={{
                        backgroundColor: 'var(--color-text-muted)20',
                        color: 'var(--color-text-muted)'
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveTemplate}
                      className="px-3 py-1 rounded text-sm text-white"
                      style={{ backgroundColor: 'var(--color-success)' }}
                    >
                      Save
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="px-3 py-1 rounded text-sm"
                    style={{
                      backgroundColor: 'var(--color-primary)20',
                      color: 'var(--color-primary)'
                    }}
                  >
                    Edit
                  </button>
                )}
              </div>
            </div>

            <div className="space-y-4">
              {/* Template Name */}
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  Template Name
                </label>
                <input
                  type="text"
                  value={selectedTemplate.name}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, name: e.target.value })}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 rounded border"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                />
              </div>

              {/* Subject Line */}
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  Subject Line
                </label>
                <input
                  type="text"
                  value={selectedTemplate.subject}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, subject: e.target.value })}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 rounded border"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                  placeholder="Use {{variableName}} for dynamic content"
                />
              </div>

              {/* HTML Content */}
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  HTML Content
                </label>
                <textarea
                  value={selectedTemplate.htmlContent}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, htmlContent: e.target.value })}
                  disabled={!isEditing}
                  rows={12}
                  className="w-full px-3 py-2 rounded border font-mono text-sm"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                  placeholder="HTML email content..."
                />
              </div>

              {/* Text Content */}
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--color-text)' }}>
                  Plain Text Content
                </label>
                <textarea
                  value={selectedTemplate.textContent}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, textContent: e.target.value })}
                  disabled={!isEditing}
                  rows={8}
                  className="w-full px-3 py-2 rounded border font-mono text-sm"
                  style={{
                    backgroundColor: 'var(--color-background)',
                    borderColor: 'var(--color-border)',
                    color: 'var(--color-text)'
                  }}
                  placeholder="Plain text email content..."
                />
              </div>

              {/* Available Variables */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
                  Available Variables
                </label>
                <div className="flex flex-wrap gap-2">
                  {selectedTemplate.variables.map((variable) => (
                    <span
                      key={variable}
                      className="px-2 py-1 rounded text-xs font-mono cursor-pointer"
                      style={{
                        backgroundColor: 'var(--color-primary)20',
                        color: 'var(--color-primary)',
                        border: '1px solid var(--color-primary)'
                      }}
                      onClick={() => {
                        if (isEditing) {
                          navigator.clipboard.writeText(`{{${variable}}}`);
                        }
                      }}
                      title={isEditing ? 'Click to copy' : ''}
                    >
                      {`{{${variable}}}`}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 flex items-center justify-center z-50" style={{
          background: 'rgba(0, 0, 0, 0.6)',
          backdropFilter: 'blur(8px)'
        }}>
          <div className="rounded-2xl p-8 w-full max-w-4xl mx-4 shadow-2xl max-h-[90vh] overflow-y-auto" style={{
            background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
            border: '2px solid var(--color-border)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
          }}>
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-4">
                <h2 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
                  Email Preview
                </h2>
                <div className="flex rounded-lg overflow-hidden border" style={{ borderColor: 'var(--color-border)' }}>
                  <button
                    onClick={() => setPreviewMode('html')}
                    className={`px-3 py-1 text-sm ${previewMode === 'html' ? 'text-white' : ''}`}
                    style={{
                      backgroundColor: previewMode === 'html' ? 'var(--color-primary)' : 'var(--color-surface)',
                      color: previewMode === 'html' ? 'white' : 'var(--color-text)'
                    }}
                  >
                    HTML
                  </button>
                  <button
                    onClick={() => setPreviewMode('text')}
                    className={`px-3 py-1 text-sm ${previewMode === 'text' ? 'text-white' : ''}`}
                    style={{
                      backgroundColor: previewMode === 'text' ? 'var(--color-primary)' : 'var(--color-surface)',
                      color: previewMode === 'text' ? 'white' : 'var(--color-text)'
                    }}
                  >
                    Text
                  </button>
                </div>
              </div>
              
              <button
                onClick={() => setShowPreview(false)}
                className="p-2 rounded-full transition-colors"
                style={{
                  color: 'var(--color-text-muted)',
                  background: 'rgba(0, 0, 0, 0.05)',
                  border: '1px solid var(--color-border)'
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              {/* Subject Preview */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
                  Subject Line
                </label>
                <div className="p-3 rounded border" style={{
                  backgroundColor: 'var(--color-background)',
                  borderColor: 'var(--color-border)'
                }}>
                  <p style={{ color: 'var(--color-text)' }}>{renderPreview().subject}</p>
                </div>
              </div>

              {/* Content Preview */}
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
                  Email Content
                </label>
                <div className="border rounded" style={{ borderColor: 'var(--color-border)' }}>
                  {previewMode === 'html' ? (
                    <div 
                      className="p-4"
                      dangerouslySetInnerHTML={{ __html: renderPreview().content }}
                      style={{ backgroundColor: 'white', minHeight: '400px' }}
                    />
                  ) : (
                    <pre 
                      className="p-4 whitespace-pre-wrap font-mono text-sm"
                      style={{
                        backgroundColor: 'var(--color-background)',
                        color: 'var(--color-text)',
                        minHeight: '400px'
                      }}
                    >
                      {renderPreview().content}
                    </pre>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailTemplateEditor;