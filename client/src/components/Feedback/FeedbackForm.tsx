import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import feedbackService, { FeedbackSubmission } from './feedbackService';

interface FeedbackFormProps {
  onSubmitSuccess?: (feedbackId: string) => void;
  onSubmitError?: (error: string) => void;
  isEmbedded?: boolean;
}

const FeedbackForm: React.FC<FeedbackFormProps> = ({
  onSubmitSuccess,
  onSubmitError,
  isEmbedded = false
}) => {
  const { currentTheme } = useTheme();
  const [formData, setFormData] = useState<FeedbackSubmission>({
    customerName: '',
    customerEmail: '',
    recipientEmail: '',
    feedbackContent: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  const handleInputChange = (field: keyof FeedbackSubmission, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear status when user starts typing
    if (submitStatus.type) {
      setSubmitStatus({ type: null, message: '' });
    }
  };

  const validateForm = (): string | null => {
    if (!formData.customerName.trim()) {
      return 'Customer name is required';
    }
    
    if (!formData.recipientEmail.trim()) {
      return 'Recipient email is required';
    }
    
    if (!formData.feedbackContent.trim()) {
      return 'Feedback content is required';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.recipientEmail)) {
      return 'Please enter a valid recipient email address';
    }

    if (formData.customerEmail && !emailRegex.test(formData.customerEmail)) {
      return 'Please enter a valid customer email address';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setSubmitStatus({ type: 'error', message: validationError });
      onSubmitError?.(validationError);
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: '' });

    try {
      const result = await feedbackService.submitFeedback(formData);
      
      if (result.success) {
        setSubmitStatus({
          type: 'success',
          message: `Feedback submitted successfully! ${result.emailSent ? 'Email sent to recipient.' : 'Email delivery pending.'}`
        });
        
        // Reset form
        setFormData({
          customerName: '',
          customerEmail: '',
          recipientEmail: '',
          feedbackContent: ''
        });
        
        onSubmitSuccess?.(result.feedbackId || '');
      } else {
        throw new Error(result.message || 'Failed to submit feedback');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setSubmitStatus({ type: 'error', message: errorMessage });
      onSubmitError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerClass = isEmbedded 
    ? "w-full max-w-2xl mx-auto"
    : "min-h-screen flex items-center justify-center p-4";

  return (
    <div className={containerClass}>
      <div 
        className={`w-full ${isEmbedded ? '' : 'max-w-2xl'} rounded-2xl p-8 shadow-2xl`}
        style={{
          background: `linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-light) 100%)`,
          border: '2px solid var(--color-border)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
        }}
      >
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2" style={{ color: 'var(--color-text)' }}>
            Send Feedback
          </h1>
          <p className="text-lg" style={{ color: 'var(--color-text-muted)' }}>
            Share your thoughts and feedback directly via email
          </p>
        </div>

        {/* Status Messages */}
        {submitStatus.type && (
          <div 
            className={`mb-6 p-4 rounded-lg border ${
              submitStatus.type === 'success' 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}
            style={{
              backgroundColor: submitStatus.type === 'success' 
                ? 'var(--color-success)20' 
                : 'var(--color-error)20',
              borderColor: submitStatus.type === 'success' 
                ? 'var(--color-success)' 
                : 'var(--color-error)',
              color: submitStatus.type === 'success' 
                ? 'var(--color-success)' 
                : 'var(--color-error)'
            }}
          >
            <div className="flex items-center">
              <svg 
                className="w-5 h-5 mr-2" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                {submitStatus.type === 'success' ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                )}
              </svg>
              {submitStatus.message}
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Customer Name */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
              Your Name *
            </label>
            <input
              type="text"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-4 py-3 rounded-xl border-2 focus:outline-none transition-all duration-200"
              style={{
                background: `linear-gradient(135deg, var(--color-background) 0%, var(--color-surface-light) 100%)`,
                borderColor: 'var(--color-border)',
                color: 'var(--color-text)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
              }}
              placeholder="Enter your full name"
              required
            />
          </div>

          {/* Customer Email (Optional) */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
              Your Email (Optional)
            </label>
            <input
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-4 py-3 rounded-xl border-2 focus:outline-none transition-all duration-200"
              style={{
                background: `linear-gradient(135deg, var(--color-background) 0%, var(--color-surface-light) 100%)`,
                borderColor: 'var(--color-border)',
                color: 'var(--color-text)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
              }}
              placeholder="<EMAIL>"
            />
            <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
              Optional: Provide your email if you want a response
            </p>
          </div>

          {/* Recipient Email */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
              Send Feedback To *
            </label>
            <input
              type="email"
              value={formData.recipientEmail}
              onChange={(e) => handleInputChange('recipientEmail', e.target.value)}
              disabled={isSubmitting}
              className="w-full px-4 py-3 rounded-xl border-2 focus:outline-none transition-all duration-200"
              style={{
                background: `linear-gradient(135deg, var(--color-background) 0%, var(--color-surface-light) 100%)`,
                borderColor: 'var(--color-border)',
                color: 'var(--color-text)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
              }}
              placeholder="<EMAIL>"
              required
            />
            <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
              Enter the email address where you want to send this feedback
            </p>
          </div>

          {/* Feedback Content */}
          <div>
            <label className="block text-sm font-medium mb-2" style={{ color: 'var(--color-text)' }}>
              Your Feedback *
            </label>
            <textarea
              value={formData.feedbackContent}
              onChange={(e) => handleInputChange('feedbackContent', e.target.value)}
              disabled={isSubmitting}
              rows={6}
              className="w-full px-4 py-3 rounded-xl border-2 focus:outline-none transition-all duration-200 resize-vertical"
              style={{
                background: `linear-gradient(135deg, var(--color-background) 0%, var(--color-surface-light) 100%)`,
                borderColor: 'var(--color-border)',
                color: 'var(--color-text)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
              }}
              placeholder="Please share your feedback, suggestions, or comments here..."
              required
            />
            <p className="text-xs mt-1" style={{ color: 'var(--color-text-muted)' }}>
              {formData.feedbackContent.length}/1000 characters
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex justify-center pt-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-8 py-4 rounded-xl font-semibold text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              style={{
                background: isSubmitting 
                  ? 'var(--color-text-muted)' 
                  : `linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%)`,
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                transform: isSubmitting ? 'none' : 'translateY(-1px)'
              }}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                    <circle 
                      className="opacity-25" 
                      cx="12" 
                      cy="12" 
                      r="10" 
                      stroke="currentColor" 
                      strokeWidth="4"
                    />
                    <path 
                      className="opacity-75" 
                      fill="currentColor" 
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  <span>Sending Feedback...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                  <span>Send Feedback</span>
                </>
              )}
            </button>
          </div>
        </form>

        {/* Footer */}
        <div className="text-center mt-8 pt-6 border-t" style={{ borderColor: 'var(--color-border)' }}>
          <p className="text-sm" style={{ color: 'var(--color-text-muted)' }}>
            Your feedback will be sent directly to the specified email address
          </p>
        </div>
      </div>
    </div>
  );
};

export default FeedbackForm;