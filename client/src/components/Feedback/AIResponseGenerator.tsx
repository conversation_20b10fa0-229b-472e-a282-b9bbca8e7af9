import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import aiAnalyticsService, { AutomatedResponse, SmartSentimentAnalysis } from './aiAnalyticsService';
import feedbackService from './feedbackService';

interface FeedbackItem {
  id: string;
  customerName: string;
  customerEmail?: string;
  feedbackContent: string;
  timestamp: string;
  sentiment?: SmartSentimentAnalysis;
  suggestedResponse?: AutomatedResponse;
  responseStatus: 'pending' | 'generated' | 'sent' | 'customized';
}

const AIResponseGenerator: React.FC = () => {
  const { currentTheme } = useTheme();
  const [feedbackItems, setFeedbackItems] = useState<FeedbackItem[]>([]);
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);
  const [customResponse, setCustomResponse] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'high_priority'>('all');

  useEffect(() => {
    loadFeedbackData();
  }, []);

  const loadFeedbackData = async () => {
    try {
      setLoading(true);
      const dashboardData = await feedbackService.getDashboardData('7d');
      
      // Convert recent submissions to feedback items
      const items: FeedbackItem[] = await Promise.all(
        dashboardData.recentSubmissions.slice(0, 10).map(async (submission) => {
          const sentiment = await aiAnalyticsService.analyzeSmartSentiment(submission.feedbackContent);
          
          return {
            id: submission.id,
            customerName: submission.customerName,
            customerEmail: submission.customerEmail,
            feedbackContent: submission.feedbackContent,
            timestamp: submission.timestamp,
            sentiment,
            responseStatus: 'pending' as const
          };
        })
      );
      
      setFeedbackItems(items);
    } catch (error) {
      console.error('Error loading feedback data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateResponse = async (feedbackItem: FeedbackItem) => {
    try {
      setIsGenerating(true);
      const response = await aiAnalyticsService.generateAutomatedResponse(
        feedbackItem.feedbackContent,
        feedbackItem.customerEmail
      );
      
      // Update the feedback item with the generated response
      setFeedbackItems(prev => prev.map(item => 
        item.id === feedbackItem.id 
          ? { ...item, suggestedResponse: response, responseStatus: 'generated' }
          : item
      ));
      
      if (selectedFeedback?.id === feedbackItem.id) {
        setSelectedFeedback({ ...feedbackItem, suggestedResponse: response, responseStatus: 'generated' });
        setCustomResponse(response.suggestedResponse);
      }
    } catch (error) {
      console.error('Error generating response:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const sendResponse = async (feedbackItem: FeedbackItem, responseText: string) => {
    try {
      // Simulate sending response (in real implementation, this would call an API)
      console.log('Sending response:', {
        to: feedbackItem.customerEmail,
        subject: `Re: Your feedback - ${feedbackItem.id}`,
        body: responseText
      });
      
      // Update status
      setFeedbackItems(prev => prev.map(item => 
        item.id === feedbackItem.id 
          ? { ...item, responseStatus: 'sent' }
          : item
      ));
      
      if (selectedFeedback?.id === feedbackItem.id) {
        setSelectedFeedback({ ...feedbackItem, responseStatus: 'sent' });
      }
      
      alert('Response sent successfully!');
    } catch (error) {
      console.error('Error sending response:', error);
      alert('Failed to send response. Please try again.');
    }
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      urgent: 'var(--color-error)',
      high: 'var(--color-warning)',
      medium: 'var(--color-info)',
      low: 'var(--color-success)'
    };
    return colors[priority as keyof typeof colors] || 'var(--color-text-muted)';
  };

  const getUrgencyColor = (urgency: string) => {
    const colors = {
      critical: 'var(--color-error)',
      high: 'var(--color-warning)',
      medium: 'var(--color-info)',
      low: 'var(--color-success)'
    };
    return colors[urgency as keyof typeof colors] || 'var(--color-text-muted)';
  };

  const getSentimentEmoji = (sentiment: SmartSentimentAnalysis) => {
    if (sentiment.overall > 0.3) return '😊';
    if (sentiment.overall < -0.3) return '😞';
    return '😐';
  };

  const filteredFeedback = feedbackItems.filter(item => {
    if (filter === 'pending') return item.responseStatus === 'pending';
    if (filter === 'high_priority') return item.sentiment?.urgency === 'high' || item.sentiment?.urgency === 'critical';
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <svg className="animate-spin w-8 h-8 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p style={{ color: 'var(--color-text-muted)' }}>Loading feedback data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: 'var(--color-text)' }}>
            AI Response Generator
          </h2>
          <p className="text-sm mt-1" style={{ color: 'var(--color-text-muted)' }}>
            Generate intelligent responses to customer feedback using AI
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Filter */}
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="px-3 py-2 rounded border text-sm"
            style={{
              backgroundColor: 'var(--color-surface)',
              borderColor: 'var(--color-border)',
              color: 'var(--color-text)'
            }}
          >
            <option value="all">All Feedback</option>
            <option value="pending">Pending Response</option>
            <option value="high_priority">High Priority</option>
          </select>
          
          <button
            onClick={loadFeedbackData}
            className="px-4 py-2 rounded-lg text-sm font-medium text-white"
            style={{ backgroundColor: 'var(--color-primary)' }}
          >
            Refresh
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Feedback List */}
        <div className="space-y-4">
          <div className="p-4 rounded-lg border" style={{
            backgroundColor: 'var(--color-surface)',
            borderColor: 'var(--color-border)'
          }}>
            <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--color-text)' }}>
              Feedback Queue ({filteredFeedback.length})
            </h3>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredFeedback.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                  </svg>
                  <p style={{ color: 'var(--color-text-muted)' }}>No feedback items found</p>
                </div>
              ) : (
                filteredFeedback.map((item) => (
                  <div
                    key={item.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-all ${
                      selectedFeedback?.id === item.id ? 'ring-2' : ''
                    }`}
                    style={{
                      backgroundColor: selectedFeedback?.id === item.id 
                        ? 'var(--color-primary)20' 
                        : 'var(--color-background)',
                      borderColor: selectedFeedback?.id === item.id 
                        ? 'var(--color-primary)' 
                        : 'var(--color-border)'
                    }}
                    onClick={() => {
                      setSelectedFeedback(item);
                      setCustomResponse(item.suggestedResponse?.suggestedResponse || '');
                    }}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-sm" style={{ color: 'var(--color-text)' }}>
                          {item.customerName}
                        </h4>
                        {item.sentiment && (
                          <span className="text-lg">{getSentimentEmoji(item.sentiment)}</span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {item.sentiment && (
                          <span className="px-2 py-1 rounded text-xs font-medium" style={{
                            backgroundColor: `${getUrgencyColor(item.sentiment.urgency)}20`,
                            color: getUrgencyColor(item.sentiment.urgency)
                          }}>
                            {item.sentiment.urgency}
                          </span>
                        )}
                        
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          item.responseStatus === 'sent' ? 'bg-green-100 text-green-800' :
                          item.responseStatus === 'generated' ? 'bg-blue-100 text-blue-800' :
                          item.responseStatus === 'customized' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.responseStatus}
                        </span>
                      </div>
                    </div>
                    
                    <p className="text-xs mb-2 line-clamp-2" style={{ color: 'var(--color-text-muted)' }}>
                      {item.feedbackContent}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        {new Date(item.timestamp).toLocaleDateString()}
                      </span>
                      
                      {item.sentiment && (
                        <div className="flex items-center space-x-1">
                          {item.sentiment.topics.slice(0, 2).map((topic, index) => (
                            <span key={index} className="px-1 py-0.5 rounded text-xs" style={{
                              backgroundColor: 'var(--color-info)20',
                              color: 'var(--color-info)'
                            }}>
                              {topic}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Response Generator */}
        <div className="space-y-4">
          {selectedFeedback ? (
            <>
              {/* Feedback Details */}
              <div className="p-4 rounded-lg border" style={{
                backgroundColor: 'var(--color-surface)',
                borderColor: 'var(--color-border)'
              }}>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
                    Feedback Details
                  </h3>
                  {selectedFeedback.sentiment && (
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{getSentimentEmoji(selectedFeedback.sentiment)}</span>
                      <span className="text-sm font-medium" style={{ color: 'var(--color-text)' }}>
                        Sentiment: {(selectedFeedback.sentiment.overall * 100).toFixed(0)}%
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                      Customer:
                    </span>
                    <span className="ml-2 text-sm" style={{ color: 'var(--color-text)' }}>
                      {selectedFeedback.customerName} ({selectedFeedback.customerEmail})
                    </span>
                  </div>
                  
                  <div>
                    <span className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                      Message:
                    </span>
                    <p className="mt-1 text-sm p-3 rounded" style={{
                      backgroundColor: 'var(--color-background)',
                      color: 'var(--color-text)'
                    }}>
                      {selectedFeedback.feedbackContent}
                    </p>
                  </div>
                  
                  {selectedFeedback.sentiment && (
                    <div>
                      <span className="text-sm font-medium" style={{ color: 'var(--color-text-muted)' }}>
                        AI Analysis:
                      </span>
                      <div className="mt-1 flex flex-wrap gap-2">
                        <span className="px-2 py-1 rounded text-xs" style={{
                          backgroundColor: `${getUrgencyColor(selectedFeedback.sentiment.urgency)}20`,
                          color: getUrgencyColor(selectedFeedback.sentiment.urgency)
                        }}>
                          Urgency: {selectedFeedback.sentiment.urgency}
                        </span>
                        {selectedFeedback.sentiment.topics.map((topic, index) => (
                          <span key={index} className="px-2 py-1 rounded text-xs" style={{
                            backgroundColor: 'var(--color-info)20',
                            color: 'var(--color-info)'
                          }}>
                            {topic}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Response Generator */}
              <div className="p-4 rounded-lg border" style={{
                backgroundColor: 'var(--color-surface)',
                borderColor: 'var(--color-border)'
              }}>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold" style={{ color: 'var(--color-text)' }}>
                    AI Response Generator
                  </h3>
                  
                  <div className="flex items-center space-x-2">
                    {selectedFeedback.suggestedResponse && (
                      <span className="px-2 py-1 rounded text-xs font-medium" style={{
                        backgroundColor: `${getPriorityColor(selectedFeedback.suggestedResponse.priority)}20`,
                        color: getPriorityColor(selectedFeedback.suggestedResponse.priority)
                      }}>
                        {selectedFeedback.suggestedResponse.priority} priority
                      </span>
                    )}
                    
                    <button
                      onClick={() => generateResponse(selectedFeedback)}
                      disabled={isGenerating}
                      className="px-3 py-1 rounded text-sm font-medium text-white disabled:opacity-50"
                      style={{ backgroundColor: 'var(--color-secondary)' }}
                    >
                      {isGenerating ? 'Generating...' : 'Generate AI Response'}
                    </button>
                  </div>
                </div>
                
                {selectedFeedback.suggestedResponse && (
                  <div className="mb-3 p-3 rounded" style={{
                    backgroundColor: 'var(--color-info)20',
                    border: '1px solid var(--color-info)'
                  }}>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium" style={{ color: 'var(--color-info)' }}>
                        AI Suggestion
                      </span>
                      <span className="text-xs" style={{ color: 'var(--color-info)' }}>
                        Confidence: {Math.round(selectedFeedback.suggestedResponse.confidence * 100)}%
                      </span>
                    </div>
                    <div className="text-xs space-y-1" style={{ color: 'var(--color-info)' }}>
                      <p>Type: {selectedFeedback.suggestedResponse.responseType}</p>
                      <p>Est. Resolution: {selectedFeedback.suggestedResponse.estimatedResolutionTime}</p>
                    </div>
                  </div>
                )}
                
                <div className="space-y-3">
                  <textarea
                    value={customResponse}
                    onChange={(e) => {
                      setCustomResponse(e.target.value);
                      if (selectedFeedback.responseStatus === 'generated') {
                        setFeedbackItems(prev => prev.map(item => 
                          item.id === selectedFeedback.id 
                            ? { ...item, responseStatus: 'customized' }
                            : item
                        ));
                        setSelectedFeedback({ ...selectedFeedback, responseStatus: 'customized' });
                      }
                    }}
                    rows={12}
                    placeholder="AI-generated response will appear here, or write your own..."
                    className="w-full px-3 py-2 rounded border resize-none"
                    style={{
                      backgroundColor: 'var(--color-background)',
                      borderColor: 'var(--color-border)',
                      color: 'var(--color-text)'
                    }}
                  />
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                        Characters: {customResponse.length}
                      </span>
                      {selectedFeedback.suggestedResponse && (
                        <span className="text-xs" style={{ color: 'var(--color-text-muted)' }}>
                          • Est. reading time: {Math.ceil(customResponse.split(' ').length / 200)} min
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setCustomResponse('')}
                        className="px-3 py-1 rounded text-sm"
                        style={{
                          backgroundColor: 'var(--color-text-muted)20',
                          color: 'var(--color-text-muted)'
                        }}
                      >
                        Clear
                      </button>
                      
                      <button
                        onClick={() => sendResponse(selectedFeedback, customResponse)}
                        disabled={!customResponse.trim() || selectedFeedback.responseStatus === 'sent'}
                        className="px-4 py-1 rounded text-sm font-medium text-white disabled:opacity-50"
                        style={{ backgroundColor: 'var(--color-success)' }}
                      >
                        {selectedFeedback.responseStatus === 'sent' ? 'Sent' : 'Send Response'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="p-8 rounded-lg border text-center" style={{
              backgroundColor: 'var(--color-surface)',
              borderColor: 'var(--color-border)'
            }}>
              <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--color-text)' }}>
                Select Feedback to Generate Response
              </h3>
              <p style={{ color: 'var(--color-text-muted)' }}>
                Choose a feedback item from the list to generate an AI-powered response
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIResponseGenerator;