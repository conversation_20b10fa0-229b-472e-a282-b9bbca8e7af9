# 📧 Email Setup Guide - Complete Configuration

## 🎯 **Quick Setup - Choose Your Email Provider**

Your feedback system can send emails through **any email provider**. Here are the most common setups:

---

## 🔧 **Option 1: Gmail (Recommended - Easiest)**

### **Step 1: Enable App Passwords**
1. Go to your **Google Account settings**
2. Navigate to **Security** → **2-Step Verification**
3. Scroll down to **App passwords**
4. Generate a new app password for "Mail"
5. **Copy the 16-character password**

### **Step 2: Update Configuration**
Replace the content in `/DATA/feedback/config/email-config.json`:

```json
{
  "service": "gmail",
  "auth": {
    "user": "<EMAIL>",
    "pass": "your-16-character-app-password"
  }
}
```

### **Step 3: Test**
```bash
# Restart your application to load new config
# Submit a test feedback to verify emails are sent
```

---

## 🔧 **Option 2: Outlook/Hotmail**

### **Configuration:**
```json
{
  "service": "hotmail",
  "auth": {
    "user": "<EMAIL>",
    "pass": "your-password"
  }
}
```

---

## 🔧 **Option 3: Custom SMTP (Any Provider)**

### **For Business Email or Custom Domains:**
```json
{
  "host": "smtp.your-domain.com",
  "port": 587,
  "secure": false,
  "auth": {
    "user": "<EMAIL>",
    "pass": "your-password"
  }
}
```

### **Common SMTP Settings:**
- **Gmail**: smtp.gmail.com, port 587
- **Outlook**: smtp-mail.outlook.com, port 587
- **Yahoo**: smtp.mail.yahoo.com, port 587
- **Custom**: Check with your email provider

---

## 🔧 **Option 4: Using Frontend Configuration**

You can also configure email through the UI:

```tsx
import { feedbackService } from './components/Feedback';

// Configure Gmail
await feedbackService.updateEmailConfig({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'your-app-password'
  }
});

// Configure Custom SMTP
await feedbackService.updateEmailConfig({
  host: 'smtp.your-domain.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'your-password'
  }
});
```

---

## 📧 **How Email Sending Works**

### **Email Flow:**
1. **Customer fills feedback form**
2. **Specifies recipient email** (where feedback should be sent)
3. **System sends email** from your configured email
4. **Recipient receives** formatted feedback email
5. **System tracks** delivery status

### **Email Content:**
```
Subject: New Feedback from [Customer Name]

Dear Team,

You have received new feedback:

From: [Customer Name] ([Customer Email])
Message: [Feedback Content]

Best regards,
Your Feedback System
```

### **Email Tracking:**
- ✅ **Sent Status**: Was email sent successfully?
- ✅ **Delivery Status**: Was email delivered?
- ✅ **Failure Tracking**: Why did email fail (if it did)?
- ✅ **Message ID**: Unique identifier for tracking

---

## 🧪 **Test Your Email Configuration**

### **Method 1: Use the Test Script**
```bash
cd /home/<USER>/PinnacleAi/client/src/components/Feedback
node testEmailConfig.js
```

### **Method 2: Submit Test Feedback**
1. Go to your feedback form
2. Fill in test data:
   - **Your name**: Test User
   - **Your email**: <EMAIL>
   - **Recipient**: <EMAIL>
   - **Message**: This is a test feedback
3. Submit and check if email arrives

### **Method 3: Check Email Logs**
```bash
# View email logs
cat /home/<USER>/PinnacleAi/DATA/feedback/daily/$(date +%Y-%m-%d)-email-logs.json
```

---

## 🔍 **Troubleshooting Email Issues**

### **Common Problems & Solutions:**

#### **Problem: "Authentication failed"**
**Solution:**
- For Gmail: Use App Password, not regular password
- For Outlook: Enable "Less secure app access"
- For Custom: Check username/password with provider

#### **Problem: "Connection refused"**
**Solution:**
- Check SMTP host and port settings
- Verify firewall isn't blocking SMTP ports
- Try different ports (587, 465, 25)

#### **Problem: "Emails not being sent"**
**Solution:**
- Check `/DATA/feedback/config/email-config.json` exists
- Verify credentials are correct
- Check email logs for error messages
- Restart application after config changes

#### **Problem: "Emails going to spam"**
**Solution:**
- Use authenticated SMTP (not free services)
- Add SPF/DKIM records to your domain
- Use business email address as sender
- Include unsubscribe link in templates

---

## 🎯 **Production Email Setup Recommendations**

### **For Small Businesses:**
- **Use Gmail** with App Password (free, reliable)
- **Limit**: 500 emails/day
- **Cost**: Free

### **For Growing Companies:**
- **Use Google Workspace** or **Microsoft 365**
- **Limit**: Higher sending limits
- **Cost**: $6-12/month per user

### **For Enterprises:**
- **Use dedicated SMTP service**:
  - **SendGrid**: 100 emails/day free, then paid
  - **Mailgun**: 5,000 emails/month free
  - **Amazon SES**: $0.10 per 1,000 emails
  - **Postmark**: $1.25 per 1,000 emails

### **Enterprise SMTP Configuration Example:**
```json
{
  "host": "smtp.sendgrid.net",
  "port": 587,
  "secure": false,
  "auth": {
    "user": "apikey",
    "pass": "your-sendgrid-api-key"
  }
}
```

---

## 📊 **Email Analytics & Tracking**

Your system automatically tracks:

### **Basic Metrics:**
- ✅ **Total Emails Sent**
- ✅ **Successful Deliveries**
- ✅ **Failed Deliveries**
- ✅ **Delivery Rate Percentage**

### **Advanced Tracking (Phase 2):**
- ✅ **Email Opens** (when recipient opens email)
- ✅ **Link Clicks** (when recipient clicks links)
- ✅ **Bounce Tracking** (when email bounces)
- ✅ **Spam Reports** (when marked as spam)

### **AI-Powered Insights (Phase 3):**
- ✅ **Delivery Optimization** suggestions
- ✅ **Best Send Times** analysis
- ✅ **Content Recommendations** for better engagement
- ✅ **Automated Alerts** for delivery issues

---

## 🔐 **Security Best Practices**

### **Protect Your Credentials:**
1. **Never commit** email passwords to version control
2. **Use App Passwords** instead of regular passwords
3. **Rotate credentials** regularly
4. **Use environment variables** in production
5. **Enable 2FA** on your email account

### **Environment Variables (Production):**
```bash
# Set environment variables
export FEEDBACK_EMAIL_USER="<EMAIL>"
export FEEDBACK_EMAIL_PASS="your-app-password"
export FEEDBACK_EMAIL_SERVICE="gmail"
```

### **Production Configuration:**
```json
{
  "service": "${FEEDBACK_EMAIL_SERVICE}",
  "auth": {
    "user": "${FEEDBACK_EMAIL_USER}",
    "pass": "${FEEDBACK_EMAIL_PASS}"
  }
}
```

---

## 🎉 **Ready to Send Emails!**

Once configured, your system will:

1. ✅ **Automatically send** feedback emails
2. ✅ **Track delivery** status and metrics
3. ✅ **Show analytics** in your dashboard
4. ✅ **Generate reports** on email performance
5. ✅ **Alert you** to any delivery issues
6. ✅ **Provide AI insights** for optimization

### **Next Steps:**
1. **Choose your email provider** (Gmail recommended for testing)
2. **Update the configuration** file or use the UI
3. **Test with a sample** feedback submission
4. **Monitor the dashboard** for email metrics
5. **Enjoy automated** feedback management!

---

**🎯 Your feedback system is now ready to send emails to any recipient worldwide!** 📧✨