import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { ThemeProvider } from '../../contexts/ThemeContext';
import FeedbackPage from './FeedbackPage';

// Test page to demonstrate the feedback system
const TestPage: React.FC = () => {
  return (
    <Router>
      <ThemeProvider>
        <div style={{ 
          minHeight: '100vh',
          backgroundColor: 'var(--color-bg)',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}>
          <FeedbackPage />
        </div>
      </ThemeProvider>
    </Router>
  );
};

export default TestPage;